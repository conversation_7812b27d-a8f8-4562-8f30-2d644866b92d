{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/header.tsx", "./src/components/layout.tsx", "./src/components/dashboard/comparisonmodal.tsx", "./src/components/dashboard/custombubble.tsx", "./src/components/dashboard/customtooltip.tsx", "./src/components/dashboard/instructordetailmodal.tsx", "./src/components/dashboard/performancedashboard.tsx", "./src/components/dashboard/performancegalaxy.tsx", "./src/components/dashboard/performancegalaxyheader.tsx", "./src/components/dashboard/statusbar.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tooltip.tsx", "./src/hooks/useprepareddata.ts", "./src/lib/performancecalculations.ts", "./src/lib/utils.ts", "./src/pages/landingpage.tsx", "./src/types/instructor.ts", "./convex/auth.config.ts", "./convex/benchmarks.ts", "./convex/cron.ts", "./convex/instructors.ts", "./convex/raw_data.ts", "./convex/schema.ts", "./convex/seed.ts", "./convex/_generated/api.d.ts", "./convex/_generated/datamodel.d.ts", "./convex/_generated/server.d.ts"], "version": "5.8.3"}