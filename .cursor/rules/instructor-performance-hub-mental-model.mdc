---
description: 
globs: 
alwaysApply: true
---
# Instructor Performance Hub: Mental Model

This document provides a comprehensive overview of the Instructor Performance Hub application, compiled from the perspectives of product management, software architecture, and development. Its purpose is to serve as a foundational "mental model" for all stakeholders involved in the project.

---

## 1. Application Overview (Product Management Perspective)

This section focuses on the "what" and "why" of the application, defining its purpose, target users, and core value proposition.

### 1.1. Purpose & Value Proposition

The **Instructor Performance Hub** is a comprehensive web-based analytics dashboard designed to visualize and track fitness instructor performance metrics. The system provides a sophisticated data visualization interface featuring an innovative "Performance Galaxy" scatter plot that positions instructors based on key performance indicators including fill rates, client retention, satisfaction ratings, and new client acquisition.

The application moves beyond simple attendance tracking to provide a weighted performance scoring system based on industry standards. The core value proposition is to empower studio management with interactive, visual insights and objective performance data to make informed decisions regarding instructor development, scheduling optimization, and performance coaching.

### 1.2. User Personas

The primary users of this application are **Studio Managers**, **Regional Directors**, and **Fitness Directors**.

- **Role:** Manages one or more studio locations. Responsible for instructor scheduling, hiring, performance reviews, and coaching.
- **Goals:**
  - Quickly identify performance patterns through visual scatter plot analysis
  - Understand instructor positioning within performance zones (Star Zone, Needs Support, etc.)
  - Track 30-day performance trends and month-over-month changes
  - Make objective, data-backed decisions about instructor development and class assignments
  - Compare instructors across multiple performance dimensions simultaneously
- **Pain Points (Addressed):**
  - Traditional tabular data doesn't reveal performance relationships and patterns
  - Difficulty in understanding multi-dimensional performance at a glance
  - Need for interactive exploration of instructor performance data
  - Lack of visual context for performance benchmarking

### 1.3. Core Features

- **Authentication:** Secure login for authorized personnel using Clerk with Convex integration
- **Performance Galaxy Visualization:** Interactive scatter plot where:
  - X-axis represents Fill Rate (class capacity utilization)
  - Y-axis represents Client Retention (percentage of returning clients)
  - Bubble size represents Satisfaction Rating (1-5 scale)
  - Color coding indicates performance status (Top Performer, Rising Star, Stable, Declining)
- **Performance Zones:** Color-coded quadrants identifying instructor categories:
  - Star Zone (high fill rate + high retention)
  - Needs Support (low fill rate + low retention)
  - Improve Retention (high fill rate + low retention)
  - Boost Fill Rate (low fill rate + high retention)
- **Status Bar:** Real-time dashboard metrics and studio averages
- **Instructor Detail Modals:** Comprehensive performance breakdowns with:
  - 30-day performance trend sparklines
  - Month-over-month change indicators
  - Detailed metric breakdowns
- **Comparison Mode:** Multi-instructor comparison functionality (UI implemented, logic pending)
- **Weighted Performance Scoring:** Industry-standard algorithm with configurable weights:
  - Fill Rate: 25% (direct revenue impact)
  - Retention: 35% (most cost-effective growth)
  - Satisfaction: 30% (drives retention and referrals)
  - Acquisition: 10% (valuable but expensive)

### 1.4. High-Level User Flow

1.  **Login:** A manager navigates to the application URL. They are presented with a landing page and authenticate via Clerk.
2.  **Performance Galaxy Overview:** Upon successful login, the user sees the main Performance Dashboard with:
     - Status bar showing key metrics and studio averages
     - Interactive scatter plot with all instructors positioned by performance metrics
     - Performance zone overlays for quick pattern recognition
3.  **Interactive Exploration:** The manager can:
     - Click on instructor bubbles to view detailed performance modals
     - Hover over bubbles for quick metric tooltips with 30-day trend sparklines
     - Observe performance patterns across the four quadrants
     - Use reference lines to compare against studio averages
4.  **Detailed Analysis:** In instructor detail modals, managers can:
     - Review comprehensive performance breakdowns
     - Analyze 30-day trend data
     - Compare month-over-month changes
5.  **Comparison Mode:** (Future feature) Select multiple instructors for side-by-side comparison
6.  **Logout:** The manager clicks the user button in the header to sign out, ending their session.

### 1.5. Current Development Status

**🟢 Fully Implemented:**
- Authentication system with Clerk integration
- Interactive Performance Galaxy scatter plot visualization
- Performance calculation algorithms with weighted metrics
- Responsive UI with modern design system (Tailwind CSS + shadcn/ui)
- Data seeding infrastructure with batch processing capabilities
- Real-time data subscriptions via Convex

**🟡 Partially Implemented:**
- Backend data processing (database schema exists, calculations incomplete)
- Comparison functionality (UI framework exists, logic is placeholder)
- Benchmark calculation system (cron job framework in place, logic incomplete)

**🔴 Critical Issues:**
- **Performance metrics are now mocked on the backend** (see HACK in `convex/instructors.ts`)
- **No automated testing infrastructure** (no Jest, Vitest, or React Testing Library setup)
- **Missing real-time data calculations** from backend (calculations need to be moved from client to Convex queries)

---

## 2. Software Architecture (Architect's Perspective)

This section describes the system's structure, its components, their interactions, and the underlying data models.

### 2.1. Architectural Style

The application employs a **Serverless, Backend-as-a-Service (BaaS) architecture** with real-time data synchronization. It is a modern **Single Page Application (SPA)** on the frontend, powered by a reactive, serverless backend.

- **Frontend:** A React 18.2.0-based SPA built with Vite 6.3.5 and TypeScript 5.8.3. Features sophisticated data visualization using Recharts 2.15.3 and modern UI components via shadcn/ui with Tailwind CSS 4.1.10.
- **Backend:** [Convex 1.24.8](mdc:https:/www.convex.dev) provides the serverless database, real-time data synchronization, and backend functions (queries/mutations). This eliminates the need for a traditional, stateful server application.
- **Authentication:** [Clerk 5.32.0](mdc:https:/clerk.com) is used as a third-party identity provider, handling user management and authentication, which is tightly integrated with Convex's authorization layer.
- **Data Pipeline:** Script-based data ingestion process with batch processing capabilities for seeding development and production environments.
- **State Management:** Zustand 5.0.5 for lightweight client-side state management.
- **Routing:** React Router DOM 7.6.2 for client-side navigation.
- **Package Management:** pnpm for efficient dependency management.

### 2.2. System & Component Diagrams

#### System Architecture Overview

This diagram illustrates the high-level components and their relationships.

```mermaid
graph TD
    subgraph User Facing
        User[Studio Manager]
    end

    subgraph Cloud Services
        Frontend[React SPA<br/>(Vite, shadcn/ui, Tailwind)]
        Backend[Convex Backend<br/>(Serverless DB, Queries, Mutations)]
        Auth[Clerk Auth<br/>(User Management, JWT)]
    end

    subgraph Offline Process
        DataProcessing[External Data Science Pipeline<br/>(Generates Metrics)]
        DataFiles(output.json, .csv files)
        UploadScript[upload.mjs script]
    end

    User -->|Interacts with| Frontend
    Frontend -->|Fetches Data & Subscribes| Backend
    Frontend -->|Authenticates via| Auth

    Backend <-->|Validates JWT| Auth

    DataProcessing --> DataFiles
    UploadScript --> |Reads| DataFiles
    UploadScript --> |Writes to| Backend

    style Frontend fill:#61DAFB
    style Backend fill:#9443FF
    style Auth fill:#6C47FF
```

#### Key Data Flows

**1. Offline Data Ingestion Flow:** This flow populates the Convex database with pre-processed performance data.

```mermaid
sequenceDiagram
    participant Ext as External Process
    participant Script as upload.mjs
    participant Convex as Convex Mutations

    Ext->>Script: Generates output.json & raw CSVs
    Script->>Convex: `uploadInstructorDataBatch`
    Script->>Convex: `uploadClassBatch`
    Script->>Convex: `uploadAttendanceRecordBatch`
    Convex-->>Script: Acknowledges writes
```

**2. Dashboard Data Request Flow:** This shows how the frontend retrieves and displays data in real-time.

```mermaid
sequenceDiagram
    participant User
    participant DashboardPage as React Dashboard Page
    participant ConvexHook as Convex `useQuery` Hook
    participant ConvexBackend as Convex Backend

    User->>DashboardPage: Loads page
    DashboardPage->>ConvexHook: Calls `useQuery(api.instructors.get)`
    ConvexHook->>ConvexBackend: Establishes real-time subscription
    ConvexBackend-->>ConvexHook: Pushes initial data & subsequent updates
    ConvexHook-->>DashboardPage: Re-renders component with new data
```

#### Frontend Component Interaction

This diagram shows how the primary UI components on the dashboard are composed and interact.

```mermaid
graph TD
    subgraph Zustand Store
        InstructorStore[(`useInstructorStore`)]
    end

    subgraph Dashboard Page
        DP[DashboardPage]
        DC1[DataCard: Total]
        DC2[DataCard: Avg Perf.]
        DC3[DataCard: Avg Conv.]
        PC[PerformanceChart]
        IT[InstructorTable]
    end

    DP --> |Renders| DC1
    DP --> |Renders| DC2
    DP --> |Renders| DC3
    DP --> |Renders| PC
    DP --> |Renders| IT

    IT --> |Reads sort state & dispatches sort actions| InstructorStore

    style IT fill:#f9f,stroke:#333,stroke-width:2px
    style InstructorStore fill:#f9f,stroke:#333,stroke-width:2px
```

### 2.3. Data Model

The data is structured across six tables within the Convex database, defined in `convex/schema.ts`.

- `instructors`: Basic instructor information (currently the only table with real data).
  - `instructor_id: string`
  - `name: string`
- `classes`: Stores raw data for every individual class session taught.
  - `class_id: string`
  - `instructor_id: string`
  - `location: string`
  - `class_type: string`
  - `class_timestamp: number` (Unix ms)
  - `time_of_day: 'am' | 'pm' | 'weekend'`
  - `capacity: number`
  - `attendance: number`
- `attendance_records`: A join table mapping clients to the classes they attended.
  - `client_id: string`
  - `class_id: string`
  - `instructor_id: string`
  - `class_timestamp: number`
- `ratings`: Client satisfaction ratings for classes.
  - `class_id: string`
  - `instructor_id: string`
  - `client_id: string`
  - `class_timestamp: number`
  - `rating: number`
- `clients`: Client information and tracking.
  - `client_id: string`
  - `name: string`
  - `first_visit_timestamp: number`
- `benchmarks`: Performance benchmarks by quarter and filters.
  - `quarter: string`
  - `metric: string`
  - `filterKey: string`
  - `value: number`

**Note:** All IDs are strings (revised from original number-based design). The `instructors` table currently only contains basic information, with performance metrics being calculated client-side using mocked data.

---

## 3. Development Details (Developer's Perspective)

This section covers the technical implementation details, coding patterns, and project structure.

### 3.1. Tech Stack

- **Programming Language:** **TypeScript 5.8.3** is used across the entire stack (frontend, backend, scripts).
- **Package Manager:** **pnpm** is used for dependency management.
- **Frontend:**
  - **Framework:** **React 18.2.0** with modern hooks and functional components
  - **Build Tool:** **Vite 6.3.5** for fast development and optimized production builds
  - **UI Components:** **shadcn/ui** (built on Radix UI 1.1.14+ and Tailwind CSS)
  - **Styling:** **Tailwind CSS 4.1.10** for utility-first styling with custom design tokens
  - **Charting:** **Recharts 2.15.3** for sophisticated data visualization (scatter plots, sparklines)
  - **Icons:** **Phosphor Icons 2.1.10** and **Lucide React 0.517.0** for comprehensive iconography
  - **Animation:** **Framer Motion 12.18.1** for smooth UI transitions
  - **Routing:** **React Router DOM 7.6.2** for client-side navigation
  - **Client-side State:** **Zustand 5.0.5** for lightweight state management
  - **Utilities:** **class-variance-authority 0.7.1**, **clsx 2.1.1**, **tailwind-merge 3.3.1**
- **Backend:**
  - **Platform:** **Convex 1.24.8** for database, serverless functions, and real-time subscriptions
  - **Helpers:** **convex-helpers 0.1.94** for additional utilities
- **Authentication:**
  - **Provider:** **Clerk 5.32.0** with Convex integration
- **Data Processing:**
  - **CSV Parsing:** **csv-parse 5.6.0** for data ingestion scripts
- **Development Tools:**
  - **Linting:** **ESLint 9.25.0** with TypeScript and React plugins
  - **PostCSS:** **autoprefixer 10.4.21** for CSS vendor prefixing

### 3.2. Project Structure

The repository is well-organized, with a clear separation of concerns.

```
/
├── convex/              # All backend code
│   ├── _generated/      # Auto-generated Convex types and API helpers
│   ├── dev-data/        # Development data files for seeding
│   ├── instructors.ts   # Queries for instructor data and dashboard
│   ├── raw_data.ts      # Mutations for ingesting raw class/attendance data
│   ├── benchmarks.ts    # Benchmark calculation logic (incomplete)
│   ├── cron.ts          # Scheduled job definitions
│   ├── seed.ts          # Data seeding functions
│   ├── schema.ts        # The single source of truth for the database schema
│   └── auth.config.ts   # Configuration for the Clerk authentication provider
│
├── public/              # Static assets (Vite favicon)
├── scripts/             # Standalone Node.js scripts
│   ├── seed.mjs         # Robust seeding script with chunked uploads
│   ├── upload.mjs       # Data parsing and upload utilities
│   ├── generate_dummy_data.sh  # Data generation scripts
│   └── deploy_data_to_prod.sh  # Production deployment scripts
│
├── src/                 # All frontend source code
│   ├── components/      # Reusable React components
│   │   ├── Dashboard/   # Performance dashboard components
│   │   │   ├── PerformanceDashboard.tsx    # Main dashboard orchestrator
│   │   │   ├── PerformanceGalaxy.tsx       # Scatter plot visualization
│   │   │   ├── InstructorDetailModal.tsx   # Detailed instructor views
│   │   │   ├── CustomBubble.tsx            # Custom scatter plot bubbles
│   │   │   ├── CustomTooltip.tsx           # Interactive tooltips
│   │   │   ├── StatusBar.tsx               # Dashboard metrics bar
│   │   │   └── ComparisonModal.tsx         # Instructor comparison (future)
│   │   ├── ui/          # shadcn/ui base components (Button, Card, Dialog, etc.)
│   │   ├── Header.tsx   # Application header with auth
│   │   └── Layout.tsx   # Main layout wrapper
│   ├── hooks/           # Custom React hooks
│   │   └── usePreparedData.ts  # Data preparation hook (currently with mocked data)
│   ├── lib/             # Utility functions
│   │   ├── utils.ts     # General utilities (cn for classnames)
│   │   └── performanceCalculations.ts  # Performance scoring algorithms
│   ├── pages/           # Top-level route components
│   │   └── LandingPage.tsx  # Unauthenticated landing page
│   ├── store/           # Zustand state management stores (minimal usage)
│   ├── types/           # TypeScript type definitions
│   │   └── instructor.ts    # Instructor performance types and constants
│   ├── App.tsx          # Root component with routing and auth guards
│   └── main.tsx         # Application entry point, providers setup
│
├── components.json      # shadcn/ui configuration
├── package.json         # Project dependencies and scripts
├── pnpm-lock.yaml       # Lockfile for pnpm
├── tsconfig.json        # TypeScript configuration for the project
├── convex.json          # Convex configuration (cron jobs)
├── vite.config.ts       # Vite build configuration
├── tailwind.config.ts   # Tailwind CSS configuration
├── postcss.config.js    # PostCSS configuration
└── eslint.config.js     # ESLint configuration
```

### 3.3. Key Implementation Details

- **Data Persistence & Access:** Data is stored in Convex. The frontend calls exported query functions (e.g., `api.instructors.getPerformanceDashboardData`) using Convex's `useQuery` hook, providing real-time, reactive data subscriptions that automatically update the UI when backend data changes.

- **Performance Calculation Architecture:**
  - **Current State (PROGRESS):** Performance metrics are now mocked backend-side in `convex/instructors.ts` to prevent critical query errors. The original client-side HACK has been removed.
  - **Intended Architecture:** Calculations should be moved to Convex queries joining `classes`, `ratings`, `attendance_records` tables, likely via a scheduled job.
  - **Weighted Scoring System:** Implemented in `lib/performanceCalculations.ts` with industry-standard weights

- **Authentication:** The frontend is wrapped in Clerk and Convex providers in `main.tsx`. `App.tsx` uses `<Authenticated>` and `<Unauthenticated>` components from `convex/react` to render different pages based on login state, effectively creating protected routes.

- **Data Visualization:**
  - **PerformanceGalaxy:** Sophisticated scatter plot using Recharts with custom bubble components
  - **Interactive Features:** Hover tooltips with sparklines, click-to-select, performance zone overlays
  - **Real-time Updates:** Automatic re-rendering when data changes via Convex subscriptions

- **Coding Patterns:**
  - **Functional React Components:** Heavy use of hooks, `useMemo`, and `useState` for state management
  - **Component-Driven Development:** Modular architecture with `Dashboard/`, `ui/`, and specialized components
  - **Type Safety:** Full TypeScript implementation with Convex-generated types for end-to-end type safety
  - **Modern CSS:** Tailwind CSS with custom design tokens and shadcn/ui component system
  - **Performance Optimization:** Proper memoization and efficient data transformations

- **Build & Deployment:**
  - **Frontend:** `pnpm build` uses Vite for optimized bundling with TypeScript compilation
  - **Backend:** Convex deployment via CLI with separate dev/prod environments
  - **Data Seeding:** Robust scripts with chunked uploads and error handling

- **Known Issues & TODOs:**
  - **🔴 CRITICAL:** Performance metrics are mocked backend-side (see `convex/instructors.ts` HACK comment)
  - **🔴 CRITICAL:** No automated testing infrastructure (no Jest, Vitest, React Testing Library)
  - **🟡 TODO:** Complete benchmark calculation logic in `convex/benchmarks.ts`
  - **🟡 TODO:** Implement real performance calculations in Convex queries
  - **🟡 TODO:** Complete comparison functionality (UI exists, logic placeholder)
  - **🟡 TODO:** Add pagination for large instructor datasets

### 3.4. Critical Technical Debt & Next Steps

**Immediate Priorities (Production Blockers):**

1.  **Move Performance Calculations to Backend**
    - **Current Issue:** All metrics in `convex/instructors.ts` are now mocked to prevent app-crashing errors.
    - **Required Action:** Implement real, *efficient* calculations in Convex, likely via scheduled functions, to replace the mock data.
    - **Files to Update:** `convex/instructors.ts`

2.  **Implement Testing Infrastructure**
    - **Current Issue:** Zero test coverage, no testing framework configured
    - **Required Action:** Add Vitest + React Testing Library, write component and integration tests
    - **Priority:** Critical for production readiness and future development confidence

3. **Complete Benchmark Calculations**
   - **Current Issue:** `convex/benchmarks.ts` contains placeholder logic with TODOs
   - **Required Action:** Implement quarter-based benchmark calculations with proper data aggregation
   - **Integration:** Ensure cron job in `convex/cron.ts` works with completed logic

**Secondary Improvements:**

4. **Implement Comparison Functionality**
   - **Current State:** UI framework exists, logic is placeholder
   - **Required Action:** Complete comparison modal with side-by-side instructor analysis

5. **Add Error Handling & Loading States**
   - **Current Gap:** Limited error boundaries and loading state management
   - **Required Action:** Add comprehensive error handling throughout the application

6. **Performance Optimization**
   - **Current Issue:** Fetches all instructor data without pagination
   - **Required Action:** Implement pagination and filtering for large datasets

---

This document reflects the current state of the Instructor Performance Hub as of the comprehensive codebase analysis. The application has a solid foundation with modern architecture and sophisticated visualization capabilities, but requires completion of backend data processing logic and testing infrastructure before production deployment.
