# Instructor Performance Hub - Style Guide & Technical Documentation

## Overview

This document provides a comprehensive technical breakdown of the Instructor Performance Hub dashboard implementation, including component architecture, styling patterns, and design systems for LLM-assisted restyling purposes.

> **Note**: For a comprehensive UX/UI analysis and refactoring recommendations, see [ux_ui_analysis.md](./ux_ui_analysis.md).

## Technology Stack

### Core Framework

- **React 18.2.0** with TypeScript
- **Vite** for build tooling
- **React Router DOM 7.6.2** for routing

### UI Framework & Styling

- **Tailwind CSS 4.1.10** - Primary styling framework
- **shadcn/ui** components (New York style variant)
- **CSS Custom Properties** for theming
- **Framer Motion 12.18.1** for animations
- **Class Variance Authority** for component variants

### Data Visualization

- **Recharts 2.15.3** - Primary charting library
- **Custom SVG components** for interactive elements

### Icons & Assets

- **Phosphor Icons React 2.1.10** - Primary icon library
- **Lucide React 0.517.0** - Secondary icons (shadcn/ui default)

## Component Architecture

### Application Structure

```
src/
├── App.tsx                    # Root component with authentication routing
├── components/
│   ├── Layout.tsx            # Main layout wrapper
│   ├── Header.tsx            # Top navigation bar
│   ├── Dashboard/            # Dashboard-specific components
│   └── ui/                   # Reusable UI components (shadcn/ui)
├── pages/
│   └── LandingPage.tsx       # Unauthenticated landing page
├── hooks/
│   └── usePreparedData.ts    # Data fetching hook
├── types/
│   └── instructor.ts         # TypeScript type definitions
└── lib/
    └── utils.ts              # Utility functions
```

### Component Hierarchy

#### Root Level

- **App.tsx**: Authentication-based routing using Convex auth
  - Unauthenticated: `LandingPage`
  - Authenticated: `Layout` → `PerformanceDashboard`

#### Layout Components

- **Layout.tsx**: Main application shell

  - Props: `{ children: React.ReactNode }`
  - Structure: Header + flexible main content area
  - Classes: `min-h-screen flex flex-col`

- **Header.tsx**: Top navigation
  - Features: Logo, title, user authentication button
  - Classes: `border-b`, `container mx-auto`, `h-16`
  - Uses: Clerk's `UserButton` component

#### Dashboard Components

##### PerformanceDashboard.tsx (Main Container)

- **Purpose**: Orchestrates the entire dashboard experience
- **State Management**:
  - `selectedInstructor`: Currently selected instructor for modal
  - `isModalOpen`: Modal visibility state
  - `compareList`: Array of instructors for comparison mode
- **Layout**: CSS Grid with responsive breakpoints
  - Mobile: Single column
  - Large screens: 3-column grid (2 cols main, 1 col sidebar)

##### PerformanceGalaxy.tsx (Primary Visualization)

- **Purpose**: Interactive scatter plot showing instructor performance
- **Key Features**:
  - Bubble chart with custom SVG bubbles
  - Performance zones with color-coded backgrounds
  - Studio average reference lines
  - Interactive comparison mode
- **Data Mapping**:
  - X-axis: Fill Rate (0-100%)
  - Y-axis: Client Retention (0-100%)
  - Bubble size: Client Satisfaction (1-5 scale)
- **Performance Zones**:
  - Red (0-50, 0-50): "Needs Support"
  - Orange (50-100, 0-50): "Improve Retention"
  - Blue (0-50, 50-100): "Boost Fill Rate"
  - Green (50-100, 50-100): "Star Zone"

##### StatusBar.tsx

- **Purpose**: Visual distribution of instructor performance statuses
- **Features**: Horizontal segmented bar with legend
- **Status Categories**:
  - Exceptional (green): `top_performer`
  - Exceeds Expectations (blue): `rising_star`
  - Meets Expectations (yellow): `stable`
  - Needs Improvement (red): `declining`

##### InstructorDetailModal.tsx

- **Purpose**: Detailed performance analysis modal
- **Sections**:
  - Header with name, status badge, performance score
  - Key metrics grid (4 radial charts)
  - 30-day performance trend (area chart)
  - Weekly pattern analysis (stacked bar chart)
  - Key insights cards
- **Charts Used**: RadialBarChart, AreaChart, BarChart

##### CustomBubble.tsx

- **Purpose**: Interactive SVG bubble for scatter plot
- **Features**:
  - Status-based coloring and icons
  - Hover effects with glow
  - Trend indicators
  - Selection rings for comparison mode
  - Rating display

##### CustomTooltip.tsx

- **Purpose**: Rich tooltip for scatter plot interactions
- **Content**: Name, performance score, 30-day sparkline, key metrics
- **Styling**: Card-based design with subtle shadows

## Styling System

### Color Palette

#### CSS Custom Properties (Light Theme)

```css
--background: oklch(1 0 0)              /* Pure white */
--foreground: oklch(0.145 0 0)          /* Near black */
--primary: oklch(0.205 0 0)             /* Dark gray */
--secondary: oklch(0.97 0 0)            /* Light gray */
--muted: oklch(0.97 0 0)                /* Light gray */
--border: oklch(0.922 0 0)              /* Light border */
--destructive: oklch(0.577 0.245 27.325) /* Red */
```

#### Performance Status Colors

- **Top Performer**: `#f59e0b` (amber-500)
- **Rising Star**: `#10b981` (emerald-500)
- **Stable**: `#3b82f6` (blue-500)
- **Declining**: `#ef4444` (red-500)

#### Chart Colors

- **Primary**: `#8b5cf6` (violet-500)
- **Secondary**: `#3b82f6` (blue-500)
- **Success**: `#10b981` (emerald-500)
- **Warning**: `#f59e0b` (amber-500)
- **Danger**: `#ef4444` (red-500)

### Typography

#### Font Stack

- **Primary**: System font stack via Tailwind defaults
- **Weights**: 400 (normal), 500 (medium), 600 (semibold), 700 (bold)

#### Text Scales

- **Headings**: `text-2xl` (24px), `text-xl` (20px), `text-lg` (18px)
- **Body**: `text-base` (16px), `text-sm` (14px), `text-xs` (12px)
- **Display**: `text-3xl` (30px), `text-4xl` (36px)

### Spacing & Layout

#### Container System

- **Max Width**: `container mx-auto` (1400px on 2xl screens)
- **Padding**: `px-4` (16px horizontal)
- **Gaps**: `gap-4` (16px), `gap-6` (24px), `gap-8` (32px)

#### Grid System

- **Dashboard**: `grid grid-cols-1 lg:grid-cols-3`
- **Metrics**: `grid grid-cols-2 md:grid-cols-4`
- **Responsive**: Mobile-first approach

#### Border Radius

- **Cards**: `rounded-lg` (8px)
- **Buttons**: `rounded-md` (6px)
- **Modals**: `rounded-xl` (12px)

### Component Patterns

#### Card Pattern

```tsx
<div className="rounded-lg bg-card p-4 shadow">
  <h3 className="mb-4 text-lg font-semibold text-card-foreground">{title}</h3>
  {content}
</div>
```

#### Button Variants (shadcn/ui)

- **Default**: Primary background with white text
- **Outline**: Border with transparent background
- **Ghost**: Transparent with hover background
- **Destructive**: Red background for dangerous actions

#### Modal Pattern (Radix UI + shadcn/ui)

```tsx
<Dialog open={isOpen} onOpenChange={setIsOpen}>
  <DialogContent className="max-w-4xl">
    <DialogHeader>
      <DialogTitle>{title}</DialogTitle>
    </DialogHeader>
    {content}
  </DialogContent>
</Dialog>
```

## Responsive Design

### Breakpoints (Tailwind defaults)

- **sm**: 640px
- **md**: 768px
- **lg**: 1024px
- **xl**: 1280px
- **2xl**: 1536px

### Mobile Considerations

- **Dashboard**: Single column layout on mobile
- **Charts**: Responsive containers with minimum heights
- **Modals**: `max-w-[90vw]` with scroll on overflow
- **Touch targets**: Minimum 44px for interactive elements

## Animation & Interactions

### Framer Motion Usage

- Currently minimal usage
- Potential for enhanced micro-interactions

### CSS Animations

- **Hover effects**: `transition-all`, `hover:scale-105`
- **Focus states**: Ring-based focus indicators
- **Loading states**: Subtle opacity changes

### Interactive States

- **Hover**: Color shifts, scale transforms, shadow changes
- **Active**: Pressed states with reduced scale
- **Focus**: Ring outlines for accessibility
- **Disabled**: Reduced opacity and pointer-events-none

## Data Flow & State Management

### Data Architecture

- **Backend**: Convex database with real-time subscriptions
- **Frontend**: React hooks for data fetching
- **Types**: Comprehensive TypeScript interfaces

### State Management

- **Local State**: React useState for component-specific state
- **Global State**: Potential for Zustand (installed but not actively used)
- **Server State**: Convex queries with automatic caching

### Key Data Types

```typescript
interface InstructorPerformance {
  instructorId: string;
  name: string;
  fillRate: number; // 0-100
  retention: number; // 0-100
  satisfaction: number; // 1-5
  acquisition: number; // Count
  performanceScore: number; // 0-100
  status: 'top_performer' | 'rising_star' | 'stable' | 'declining';
  trend: 'rising' | 'stable' | 'falling';
  monthOverMonthChange: number;
  last30Days: { date: string; score: number }[];
}
```

## Build Configuration

### Vite Configuration

- **TypeScript**: Full type checking
- **Path aliases**: `@/` maps to `src/`
- **Hot reload**: Development server with fast refresh

### PostCSS Plugins

- **Tailwind CSS**: Main styling processor
- **Autoprefixer**: Browser compatibility

### Dependencies Summary

- **UI**: React, React DOM, React Router
- **Styling**: Tailwind CSS, shadcn/ui components
- **Charts**: Recharts
- **Icons**: Phosphor Icons, Lucide React
- **Auth**: Clerk React
- **Backend**: Convex
- **Utils**: clsx, tailwind-merge, class-variance-authority

## Custom CSS Classes & Patterns

### Dashboard-Specific CSS (Dashboard.css)

```css
/* Modal overlay with backdrop blur */
.modal-overlay {
  background-color: rgba(10, 10, 20, 0.8);
  backdrop-filter: blur(8px);
}

/* Status badge variants */
.status-top_performer {
  @apply bg-amber-100 text-amber-800;
}
.status-rising_star {
  @apply bg-emerald-100 text-emerald-800;
}
.status-stable {
  @apply bg-blue-100 text-blue-800;
}
.status-declining {
  @apply bg-red-100 text-red-800;
}

/* Animation classes */
.pulse-animation {
  animation: pulse 2s infinite;
}
.rotate-animation {
  animation: rotate 3s linear infinite;
}
```

### Utility Class Patterns

- **Flexbox**: `flex items-center justify-between`
- **Grid**: `grid grid-cols-1 lg:grid-cols-3 gap-4`
- **Spacing**: `p-4`, `px-6`, `py-2`, `mb-4`, `mt-6`
- **Colors**: `text-gray-600`, `bg-white`, `border-gray-200`
- **Shadows**: `shadow-sm`, `shadow-lg`

## Chart Configuration Patterns

### Recharts Common Props

```tsx
// Responsive container wrapper
<ResponsiveContainer width="100%" height={400}>
  <ScatterChart margin={{ top: 20, right: 30, bottom: 40, left: 30 }}>
    {/* Chart content */}
  </ScatterChart>
</ResponsiveContainer>

// Axis configuration
<XAxis
  type="number"
  domain={[0, 100]}
  tick={{ fill: '#6b7280', fontSize: 12 }}
  label={{ value: 'Fill Rate →', position: 'insideBottom' }}
/>

// Custom tooltip
<Tooltip content={<CustomTooltip />} cursor={{ strokeDasharray: '3 3' }} />
```

### Performance Zone Configuration

```tsx
const performanceZones = [
  { x1: 0, x2: 50, y1: 0, y2: 50, fill: 'rgba(239, 68, 68, 0.05)' },
  { x1: 50, x2: 100, y1: 0, y2: 50, fill: 'rgba(251, 146, 60, 0.05)' },
  { x1: 0, x2: 50, y1: 50, y2: 100, fill: 'rgba(59, 130, 246, 0.05)' },
  { x1: 50, x2: 100, y1: 50, y2: 100, fill: 'rgba(34, 197, 94, 0.05)' },
];
```

## Accessibility Considerations

### ARIA Labels & Screen Reader Support

- **Modal titles**: `DialogTitle` with `sr-only` class when needed
- **Button labels**: Descriptive text for icon-only buttons
- **Chart accessibility**: Alternative text descriptions for data visualizations

### Keyboard Navigation

- **Focus management**: Proper tab order through interactive elements
- **Modal focus**: Trapped focus within modal dialogs
- **Button states**: Clear focus indicators with ring utilities

### Color Contrast

- **Text contrast**: Meets WCAG AA standards
- **Interactive elements**: Sufficient contrast for hover/focus states
- **Chart colors**: Distinguishable for color-blind users

## Performance Optimizations

### React Optimizations

- **useMemo**: Expensive calculations cached (e.g., chart data transformations)
- **useCallback**: Event handlers memoized where appropriate
- **Component splitting**: Large components broken into smaller pieces

### Bundle Optimization

- **Tree shaking**: Unused code eliminated by Vite
- **Code splitting**: Potential for route-based splitting
- **Asset optimization**: SVG icons and images optimized

### Data Loading

- **Real-time updates**: Convex provides efficient subscriptions
- **Loading states**: Graceful handling of data fetching
- **Error boundaries**: Potential for error handling improvements

## Testing Considerations

### Component Testing Patterns

```tsx
// Example test structure for dashboard components
describe('PerformanceDashboard', () => {
  it('renders loading state when data is unavailable', () => {
    // Test loading state
  });

  it('displays instructor bubbles when data is loaded', () => {
    // Test data visualization
  });

  it('opens modal when instructor is selected', () => {
    // Test interaction
  });
});
```

### Visual Regression Testing

- **Storybook integration**: Potential for component documentation
- **Screenshot testing**: Visual consistency across browsers
- **Responsive testing**: Layout verification across breakpoints

## Future Enhancement Opportunities

### Design System Improvements

- **Design tokens**: Centralized color, spacing, and typography tokens
- **Component variants**: Extended shadcn/ui component library
- **Animation library**: Consistent micro-interactions

### Performance Enhancements

- **Virtual scrolling**: For large instructor lists
- **Chart optimization**: Canvas-based rendering for complex visualizations
- **Progressive loading**: Incremental data loading strategies

### Accessibility Enhancements

- **High contrast mode**: Alternative color schemes
- **Reduced motion**: Respect user preferences
- **Screen reader optimization**: Enhanced chart descriptions

## New Color Palette Integration

### Base Color Palette

- **Midnight Green**: `#1a535c` - Primary brand color
- **Robin Egg Blue**: `#4ecdc4` - Secondary/accent color
- **Mint Cream**: `#f7fff7` - Light background
- **Light Red**: `#ff6b6b` - Warning/error states
- **Naples Yellow**: `#ffe66d` - Success/highlight color

### Updated CSS Custom Properties

```css
:root {
  /* Base colors */
  --background: #f7fff7; /* Mint Cream - main background */
  --foreground: #1a535c; /* Midnight Green - primary text */
  --card: #ffffff; /* Pure white for card backgrounds */
  --card-foreground: #1a535c; /* Midnight Green for card text */

  /* Primary brand colors */
  --primary: #1a535c; /* Midnight Green - primary actions */
  --primary-foreground: #f7fff7; /* Mint Cream - text on primary */

  /* Secondary colors */
  --secondary: #e8f8f7; /* Light tint of Robin Egg Blue */
  --secondary-foreground: #1a535c; /* Midnight Green text */

  /* Accent colors */
  --accent: #4ecdc4; /* Robin Egg Blue - highlights */
  --accent-foreground: #1a535c; /* Midnight Green text on accent */

  /* Muted colors */
  --muted: #e8f4f5; /* Very light Midnight Green tint */
  --muted-foreground: #4a6b70; /* Medium Midnight Green */

  /* Border and input colors */
  --border: #c7e0e2; /* Light Robin Egg Blue tint */
  --input: #e8f4f5; /* Light background for inputs */
  --ring: #4ecdc4; /* Robin Egg Blue for focus rings */

  /* Status colors */
  --destructive: #ff6b6b; /* Light Red for errors */
  --destructive-foreground: #ffffff; /* White text on red */
}
```

### Performance Status Colors

```css
/* Updated status badge classes */
.status-top_performer {
  background-color: #fff9e6; /* Light Naples Yellow background */
  color: #8b6914; /* Dark yellow text */
  border: 1px solid #ffe66d;
}

.status-rising_star {
  background-color: #e8f8f7; /* Light Robin Egg Blue background */
  color: #1a535c; /* Midnight Green text */
  border: 1px solid #4ecdc4;
}

.status-stable {
  background-color: #f0faf9; /* Very light blue-green */
  color: #2a5a61; /* Medium green text */
  border: 1px solid #7dd3c0;
}

.status-declining {
  background-color: #fff0f0; /* Light red background */
  color: #b91c1c; /* Dark red text */
  border: 1px solid #ff6b6b;
}
```

### Chart Colors

```tsx
// Updated performance zones
const performanceZones = [
  {
    x1: 0,
    x2: 50,
    y1: 0,
    y2: 50,
    fill: 'rgba(255, 107, 107, 0.08)', // Light Red - Needs Support
    label: 'Needs Support',
  },
  {
    x1: 50,
    x2: 100,
    y1: 0,
    y2: 50,
    fill: 'rgba(255, 230, 109, 0.08)', // Naples Yellow - Improve Retention
    label: 'Improve Retention',
  },
  {
    x1: 0,
    x2: 50,
    y1: 50,
    y2: 100,
    fill: 'rgba(78, 205, 196, 0.08)', // Robin Egg Blue - Boost Fill Rate
    label: 'Boost Fill Rate',
  },
  {
    x1: 50,
    x2: 100,
    y1: 50,
    y2: 100,
    fill: 'rgba(26, 83, 92, 0.08)', // Midnight Green - Star Zone
    label: 'Star Zone',
  },
];

// Chart color palette
const chartColors = {
  primary: '#1a535c', // Midnight Green
  secondary: '#4ecdc4', // Robin Egg Blue
  tertiary: '#ffe66d', // Naples Yellow
  quaternary: '#ff6b6b', // Light Red
  accent: '#7dd3c0', // Light Robin Egg Blue
};
```

### Button Styling

```css
/* Primary button */
.btn-primary {
  background-color: #1a535c; /* Midnight Green */
  color: #f7fff7; /* Mint Cream text */
  border: 1px solid #1a535c;
}

.btn-primary:hover {
  background-color: #2a6b73; /* Darker Midnight Green */
}

/* Secondary button */
.btn-secondary {
  background-color: #4ecdc4; /* Robin Egg Blue */
  color: #1a535c; /* Midnight Green text */
  border: 1px solid #4ecdc4;
}

/* Outline button */
.btn-outline {
  background-color: transparent;
  color: #1a535c; /* Midnight Green text */
  border: 1px solid #4ecdc4; /* Robin Egg Blue border */
}

.btn-outline:hover {
  background-color: #4ecdc4; /* Robin Egg Blue background */
  color: #1a535c; /* Midnight Green text */
}
```

This comprehensive style guide provides the foundation for understanding and modifying the current implementation. The system is built with modern React patterns, comprehensive TypeScript typing, and a cohesive design system based on Tailwind CSS and shadcn/ui components.
