# Comprehensive Analysis: Instructor Performance Measurement System

## 1. Performance Metrics and KPIs

The system tracks four core performance metrics for instructor evaluation:

### 1.1 Fill Rate (25% weight)

- **Definition**: Percentage of class spots filled (attendance/capacity)
- **Range**: 0-100%
- **Calculation**: `(total_attendance / total_capacity) * 100`
- **Data Sources**: `classes` table (`attendance`, `capacity` fields)
- **Business Logic**: Normalized against studio historical averages for fairness across different class types, time slots, and locations
- **Critical Threshold**: <50% triggers warning indicators

### 1.2 Client Retention (35% weight - highest priority)

- **Definition**: Percentage of new clients who return within a specific timeframe
- **Range**: 0-100%
- **Calculation**: Based on client attendance patterns from `attendance_records` table
- **Data Sources**: `attendance_records`, `clients` tables
- **Business Logic**: Tracks clients who return specifically to that instructor's classes within 30 days
- **Critical Threshold**: <60% triggers warning indicators

### 1.3 Client Satisfaction (30% weight)

- **Definition**: Average client rating from post-class surveys
- **Range**: 1-5 scale
- **Calculation**: Average of all ratings from `ratings` table
- **Data Sources**: `ratings` table (`rating` field)
- **Business Logic**: Automatically sent and tracked via FitGrid integration
- **Normalization**: Converted to 0-100 scale using `((satisfaction - 1) / 4) * 100`
- **Critical Threshold**: <3.5 triggers warning indicators

### 1.4 New Client Acquisition (10% weight)

- **Definition**: Number of new clients acquired by instructor
- **Range**: Absolute count
- **Calculation**: Count of first-time clients attending instructor's classes
- **Data Sources**: `clients`, `attendance_records` tables
- **Business Logic**: Tracks first-time studio visitors in instructor's classes
- **Normalization**: Converted to 0-100 scale using `Math.min((acquisition / 10) * 100, 100)` (assumes 10 new clients/month is excellent)

## 2. Calculation Methods and Algorithms

### 2.1 Weighted Performance Score

```typescript
export const calculatePerformanceScore = (
  metrics: InstructorMetrics
): number => {
  // Normalize acquisition to 0-100 scale (assume 10 new clients/month is excellent)
  const normalizedAcquisition = Math.min((metrics.acquisition / 10) * 100, 100);

  // Normalize satisfaction from 1-5 to 0-100
  const normalizedSatisfaction = ((metrics.satisfaction - 1) / 4) * 100;

  const score =
    metrics.fillRate * PERFORMANCE_WEIGHTS.fillRate +
    metrics.retention * PERFORMANCE_WEIGHTS.retention +
    normalizedSatisfaction * PERFORMANCE_WEIGHTS.satisfaction +
    normalizedAcquisition * PERFORMANCE_WEIGHTS.acquisition;

  return Math.max(0, Math.min(score, 100)); // Clamp score between 0 and 100
};
```

### 2.2 Performance Weights (Industry Standards)

```typescript
export const PERFORMANCE_WEIGHTS = {
  /** 25% - Direct revenue impact */
  fillRate: 0.25,
  /** 35% - Most cost-effective growth */
  retention: 0.35,
  /** 30% - Drives retention and referrals */
  satisfaction: 0.3,
  /** 10% - Valuable but expensive */
  acquisition: 0.1,
};
```

### 2.3 Status Determination Logic

```typescript
export const determineStatus = (
  monthOverMonthChange: number,
  currentScore: number
): InstructorPerformance['status'] => {
  if (currentScore >= 85 && monthOverMonthChange >= 0) return 'top_performer';
  if (monthOverMonthChange > STATUS_THRESHOLDS.RISING) return 'rising_star';
  if (monthOverMonthChange < STATUS_THRESHOLDS.DECLINING) return 'declining';
  return 'stable';
};
```

### 2.4 Status Thresholds

- **Top Performer**: Score ≥85 AND month-over-month change ≥0%
- **Rising Star**: Month-over-month change >10%
- **Declining**: Month-over-month change <-5%
- **Stable**: All other cases

## 3. Data Flow Architecture

### 3.1 Data Collection Sources

1. **Classes Data**: `convex/dev-data/classes.json`

   - Fields: `class_id`, `instructor_id`, `location`, `class_type`, `class_timestamp`, `capacity`, `attendance`
   - Time categorization: Automatic `time_of_day` calculation (am/pm/weekend)

2. **Ratings Data**: `convex/dev-data/ratings.json`

   - Fields: `class_id`, `instructor_id`, `client_id`, `rating`, `class_timestamp`
   - Source: Post-class surveys via FitGrid integration

3. **Attendance Records**: `convex/dev-data/attendance_records.json`

   - Fields: `client_id`, `class_id`, `instructor_id`, `class_timestamp`
   - Used for retention and acquisition calculations

4. **Client Data**: `convex/dev-data/clients.json`
   - Fields: `client_id`, `name`, `first_visit_timestamp`
   - Used for new client identification

### 3.2 Data Processing Pipeline

#### Current Implementation (Temporary)

The system currently uses mocked data due to scalability issues with real-time calculations:

```typescript
// HACK: This is a temporary fix to avoid the "Too many documents read" error.
// The previous implementation tried to calculate real metrics on the fly, which was
// not scalable. This version mocks all performance data on the backend.
const performanceData = instructors.map((instructor) => {
  // --- ALL METRICS ARE CURRENTLY MOCKED ---
  const fillRate = Math.random() * 50 + 50; // Random fill rate between 50% and 100%
  const satisfaction = Math.random() * 2 + 3; // Random rating between 3.0 and 5.0
  const retention = Math.random() * 50 + 50; // Random retention between 50% and 100%
  const acquisition = Math.random() * 20; // Random new client acquisition
```

#### Intended Architecture (Production)

1. **Scheduled Aggregation**: Daily cron job at 8:00 AM UTC
2. **Pre-calculated Metrics**: Store computed values in `benchmarks` table
3. **Real-time Queries**: Fast retrieval of pre-computed performance data
4. **Data Freshness**: 90-day rolling window for performance calculations

### 3.3 Benchmark Calculation System

```typescript
crons.daily(
  'calculateDailyBenchmarks', // Job name
  { hourUTC: 8, minuteUTC: 0 }, // Run every day at 8:00 AM UTC
  internal.benchmarks.calculateAndStoreAllBenchmarks // The function to run
);
```

## 4. Dashboard Presentation and Visualization

### 4.1 Performance Galaxy Visualization

- **Type**: Interactive scatter plot using Recharts library
- **X-Axis**: Fill Rate (0-100%)
- **Y-Axis**: Client Retention (0-100%)
- **Bubble Size**: Satisfaction Rating (1-5 scale)
- **Color Coding**: Performance status (Top Performer, Rising Star, Stable, Declining)

#### Performance Zones

```typescript
const performanceZones = [
  {
    x1: 0,
    x2: avgFillRate,
    y1: 0,
    y2: avgRetention,
    fill: 'rgba(239, 68, 68, 0.15)', // Critical zone - needs support
    label: 'Needs Support',
  },
  {
    x1: avgFillRate,
    x2: 100,
    y1: 0,
    y2: avgRetention,
    fill: 'rgba(251, 146, 60, 0.12)', // Improve retention zone
    label: 'Improve Retention',
  },
  {
    x1: 0,
    x2: avgFillRate,
    y1: avgRetention,
    y2: 100,
    fill: 'rgba(34, 197, 94, 0.12)', // Boost fill rate zone
    label: 'Boost Fill Rate',
  },
  {
    x1: avgFillRate,
    x2: 100,
    y1: avgRetention,
    y2: 100,
    fill: 'rgba(59, 130, 246, 0.15)', // Star zone
    label: 'Star Zone',
  },
];
```

### 4.2 Status Distribution Bar

- **Visual**: Segmented horizontal bar chart
- **Categories**: Exceptional, Exceeds Expectations, Meets Expectations, Needs Improvement
- **Interactive**: Hover tooltips with instructor lists
- **Alerts**: Pulsing animation for critical status instructors

### 4.3 KPI Summary Cards

```typescript
// Calculate dashboard metrics
const totalInstructors = preparedData.length;
const avgPerformanceScore =
  preparedData.reduce((sum, inst) => sum + inst.performanceScore, 0) /
  totalInstructors;
const avgFillRate =
  preparedData.reduce((sum, inst) => sum + inst.fillRate, 0) / totalInstructors;
const avgRetention =
  preparedData.reduce((sum, inst) => sum + inst.retention, 0) /
  totalInstructors;
```

### 4.4 Instructor Detail Modal

- **Performance Trend**: 30-day area chart showing score evolution
- **Metric Breakdown**: Radial charts for Fill Rate, Retention, Satisfaction
- **Class Pattern**: Weekly stacked bar chart showing time slot performance
- **Critical Alerts**: Visual warnings for underperforming metrics

## 5. Business Logic and Rules

### 5.1 Eligibility Criteria

- **Minimum Classes**: Instructors must have taught at least 5 classes in the quarter
- **Data Completeness**: All required metrics must be available
- **Time Window**: Performance calculated on 90-day rolling window

### 5.2 Performance Rating Scales

- **Exceptional (Top Performer)**: Score ≥85 with positive trend
- **Exceeds Expectations (Rising Star)**: Month-over-month improvement >10%
- **Meets Expectations (Stable)**: Consistent performance within normal ranges
- **Needs Improvement (Declining)**: Month-over-month decline >5%

### 5.3 Critical Thresholds and Alerts

- **Fill Rate**: <50% triggers warning
- **Retention**: <60% triggers warning
- **Satisfaction**: <3.5 triggers warning
- **Performance Score**: <60% triggers critical attention
- **Visual Indicators**: Pulsing animations, color coding, warning icons

### 5.4 Normalization Rules

- **Fill Rate**: Normalized against studio historical averages by class type, time slot, and location
- **Satisfaction**: Converted from 1-5 scale to 0-100 percentage
- **Acquisition**: Capped at 100% assuming 10 new clients/month as excellent performance

## 6. Technology Stack and Implementation

### 6.1 Backend (Convex)

- **Database**: Convex serverless database
- **Schema**: Defined in `convex/schema.ts`
- **Queries**: Real-time reactive queries
- **Mutations**: Data upload and processing functions
- **Cron Jobs**: Scheduled benchmark calculations

### 6.2 Frontend (React + TypeScript)

- **Framework**: React 18 with TypeScript
- **State Management**: Zustand for global state
- **UI Components**: shadcn/ui component library
- **Charts**: Recharts for data visualization
- **Styling**: Tailwind CSS with custom animations
- **Icons**: Phosphor Icons and Lucide React

### 6.3 Data Processing

- **Upload Scripts**: Node.js scripts for batch data processing
- **Chunked Processing**: Large datasets processed in configurable chunks
- **Error Handling**: Comprehensive error handling and retry logic

## 7. Code References and File Structure

### 7.1 Core Performance Logic

- `src/lib/performanceCalculations.ts` - Weighted scoring algorithms
- `src/types/instructor.ts` - Type definitions and constants
- `convex/instructors.ts` - Backend performance data queries

### 7.2 Visualization Components

- `src/components/Dashboard/PerformanceDashboard.tsx` - Main dashboard orchestrator
- `src/components/Dashboard/PerformanceGalaxy.tsx` - Scatter plot visualization
- `src/components/Dashboard/InstructorDetailModal.tsx` - Detailed instructor views
- `src/components/Dashboard/StatusBar.tsx` - Status distribution display

### 7.3 Data Management

- `convex/schema.ts` - Database schema definitions
- `convex/raw_data.ts` - Data upload mutations
- `convex/benchmarks.ts` - Benchmark calculation logic
- `scripts/seed.mjs` - Data seeding and migration scripts

### 7.4 Configuration and Setup

- `convex/cron.ts` - Scheduled job definitions
- `package.json` - Dependencies and build configuration
- `tailwind.config.ts` - Styling configuration

## 8. Database Schema

### 8.1 Core Tables

```typescript
// Instructors table
instructors: {
  instructor_id: string,
  name: string,
}

// Classes table
classes: {
  class_id: string,
  instructor_id: string,
  location: string,
  class_type: string,
  class_timestamp: number,
  time_of_day: 'am' | 'pm' | 'weekend',
  capacity: number,
  attendance: number,
}

// Ratings table
ratings: {
  class_id: string,
  instructor_id: string,
  client_id: string,
  class_timestamp: number,
  rating: number,
}

// Attendance records table
attendance_records: {
  client_id: string,
  class_id: string,
  instructor_id: string,
  class_timestamp: number,
}

// Clients table
clients: {
  client_id: string,
  name: string,
  first_visit_timestamp: number,
}

// Benchmarks table
benchmarks: {
  quarter: string,
  metric: string,
  filterKey: string,
  value: number,
}
```

### 8.2 Indexes

- `instructors`: `by_instructor_id` on `instructor_id`
- `classes`: `by_instructor_id` on `instructor_id`
- `attendance_records`: `by_client_id_and_time` on `[client_id, class_timestamp]`, `by_instructor_id` on `instructor_id`
- `ratings`: `by_instructor_id` on `instructor_id`
- `benchmarks`: `by_key` on `[quarter, metric, filterKey]`
- `clients`: `by_client_id` on `client_id`

## 9. Data Processing Algorithms

### 9.1 Fill Rate Calculation

```python
# From scripts/generate_dummy_data.sh
def calculate_fill_rate(instructor_classes):
    total_attendance = sum(c['attendance'] for c in instructor_classes)
    total_capacity = sum(c['capacity'] for c in instructor_classes)
    fill_rate = (total_attendance / total_capacity) * 100 if total_capacity > 0 else 0
    return round(fill_rate, 2)
```

### 9.2 Retention Calculation

```python
# Retention logic from data generation script
def calculate_retention(instructor_id, quarter_start, quarter_end):
    # Find clients who attended instructor's classes for the first time in this quarter
    new_clients = get_new_clients_for_instructor(instructor_id, quarter_start, quarter_end)

    # Check how many returned within 30 days
    retained_clients = 0
    for client_id in new_clients:
        first_attendance = get_first_attendance(client_id, instructor_id)
        subsequent_attendance = get_attendance_after_date(
            client_id, instructor_id, first_attendance + 30_days
        )
        if subsequent_attendance:
            retained_clients += 1

    retention_rate = (retained_clients / len(new_clients)) * 100 if new_clients else 0
    return round(retention_rate, 2)
```

### 9.3 Satisfaction Calculation

```python
def calculate_satisfaction(instructor_ratings):
    if not instructor_ratings:
        return 0

    total_rating = sum(r['rating'] for r in instructor_ratings)
    avg_rating = total_rating / len(instructor_ratings)
    return round(avg_rating, 2)
```

### 9.4 Acquisition Calculation

```python
def calculate_acquisition(instructor_id, quarter_start, quarter_end):
    # Count clients whose first studio visit was to this instructor's class
    new_clients = 0
    for client in all_clients:
        if (quarter_start <= client['first_visit_timestamp'] <= quarter_end and
            client['first_instructor'] == instructor_id):
            new_clients += 1

    return new_clients
```

## 10. User Interface Components and Interactions

### 10.1 Dashboard Layout

- **Header**: Application title, performance calculation explanation modal, KPI summary cards
- **Main Content**: Performance Galaxy visualization with interactive zones
- **Sidebar**: Status distribution bar with instructor categorization
- **Modals**: Detailed instructor performance breakdowns

### 10.2 Interactive Features

- **Bubble Click**: Opens detailed instructor modal
- **Zone Highlighting**: Visual feedback for performance quadrants
- **Hover Tooltips**: Contextual information on data points
- **Status Filtering**: Click status categories to view instructor lists
- **Comparison Mode**: Multi-instructor selection (UI implemented, logic pending)

### 10.3 Visual Design System

- **Color Palette**:
  - Primary: Robin Egg Blue (#4ecdc4)
  - Accent: Naples Yellow (#ffe66d)
  - Warning: Light Red (#ff6b6b)
  - Success: Emerald Green (#22c55e)
- **Typography**: Montserrat font family
- **Animations**: Pulse effects for critical alerts, smooth transitions
- **Icons**: Phosphor Icons (duotone style) and Lucide React

### 10.4 Responsive Design

- **Desktop**: Full dashboard with side-by-side layout
- **Mobile**: Stacked layout with collapsible sections
- **Tablet**: Adaptive grid system for optimal viewing

## 11. Performance Optimization and Scalability

### 11.1 Current Limitations

- **Query Limits**: "Too many documents read" errors with large datasets
- **Real-time Calculations**: Performance bottlenecks with on-the-fly metric computation
- **Memory Usage**: Large data sets cause browser performance issues

### 11.2 Optimization Strategies

- **Pre-aggregation**: Move calculations to scheduled background jobs
- **Caching**: Store computed metrics in dedicated tables
- **Pagination**: Implement data chunking for large result sets
- **Indexing**: Optimize database queries with proper indexes

### 11.3 Scalability Solutions

- **Batch Processing**: Process data in configurable chunks (currently 7000 records)
- **Incremental Updates**: Only recalculate changed data
- **Background Jobs**: Use Convex cron jobs for heavy computations
- **Data Archiving**: Implement data retention policies for historical data

## 12. Current System Status and Technical Debt

### 12.1 Known Issues

1. **Performance Calculations**: Currently using mocked data due to scalability issues
2. **Real-time Calculations**: Need to implement proper aggregation pipeline
3. **Benchmark System**: Placeholder implementation needs completion
4. **Data Volume**: Large datasets cause "Too many documents read" errors
5. **Code Duplication**: Performance weights defined in both frontend and backend

### 12.2 Planned Improvements

1. **Cron-based Aggregation**: Move calculations to scheduled background jobs
2. **Pre-computed Metrics**: Store calculated values for fast retrieval
3. **Incremental Updates**: Process only changed data for efficiency
4. **Comparison Features**: Complete multi-instructor comparison functionality
5. **Real Data Integration**: Replace mocked data with actual calculations

### 12.3 Technical Debt Items

- **Backend/Frontend Sync**: Consolidate performance weight constants
- **Error Handling**: Improve error handling for failed calculations
- **Testing**: Add comprehensive unit tests for calculation logic
- **Documentation**: Complete API documentation for all endpoints
- **Monitoring**: Add performance monitoring and alerting

## 13. Data Security and Privacy

### 13.1 Authentication

- **Provider**: Clerk authentication service
- **Integration**: Convex-Clerk integration for secure backend access
- **Authorization**: Role-based access control (implementation pending)

### 13.2 Data Protection

- **Client Data**: Personal information stored securely with unique identifiers
- **Performance Data**: Instructor metrics protected by authentication
- **Data Retention**: Configurable retention policies for historical data

### 13.3 Privacy Considerations

- **Anonymization**: Client IDs used instead of personal information in calculations
- **Access Control**: Restricted access to performance data
- **Audit Trail**: Track data access and modifications (implementation pending)

## 14. Integration Points

### 14.1 External Systems

- **FitGrid**: Post-class survey integration for satisfaction ratings
- **Studio Management**: Class scheduling and capacity data
- **Payment Systems**: Revenue tracking integration (future enhancement)

### 14.2 Data Import/Export

- **CSV Import**: Batch data upload via scripts
- **JSON Export**: Performance data export for reporting
- **API Endpoints**: RESTful API for external integrations (future)

### 14.3 Reporting Integration

- **Dashboard Export**: PDF/PNG export of visualizations (future)
- **Email Reports**: Automated performance summaries (future)
- **Analytics**: Integration with business intelligence tools (future)

## 15. Conclusion

This comprehensive analysis documents the current state of the instructor performance measurement system, providing a complete technical specification for understanding, maintaining, and improving the system. The analysis covers all aspects from data collection and processing to visualization and user interaction, serving as a foundation for future enhancements and optimizations.

The system demonstrates a sophisticated approach to performance measurement with industry-standard weighted scoring, interactive visualizations, and comprehensive business logic. While currently using mocked data due to scalability challenges, the architecture is well-designed for transitioning to real-time calculations with proper optimization strategies.

Key areas for immediate improvement include implementing the cron-based aggregation pipeline, completing the benchmark calculation system, and addressing the technical debt items identified throughout the codebase.
