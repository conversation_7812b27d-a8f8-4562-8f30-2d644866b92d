import { query } from './_generated/server';
import { v } from 'convex/values';

// This query fetches all raw data for the dashboard within a 90-day window.
export const getDashboardData = query({
  args: {},
  handler: async (ctx) => {
    const ninetyDaysAgo = Date.now() - 90 * 24 * 60 * 60 * 1000;

    const instructors = await ctx.db.query('instructors').collect();
    const recentClasses = await ctx.db
      .query('classes')
      .filter((q) => q.gt(q.field('class_timestamp'), ninetyDaysAgo))
      .collect();
    const recentRatings = await ctx.db.query('ratings').collect(); // In a real app, filter this by timestamp too.
    const recentAttendance = await ctx.db.query('attendance_records').collect(); // In a real app, this would need to be optimized.

    // NOTE: For a production app with large datasets, fetching all attendance
    // is not scalable. This would be replaced by more targeted queries or aggregations.
    // For this implementation, we will perform joins/lookups on the client.

    return {
      instructors,
      classes: recentClasses,
      ratings: recentRatings,
      attendance: recentAttendance,
    };
  },
});

// This query fetches the calculated benchmarks.
export const getBenchmarks = query({
  args: { quarter: v.string(), filterKeys: v.array(v.string()) },
  handler: async (ctx, args) => {
    const benchmarks = new Map<string, number>();
    for (const key of args.filterKeys) {
      const metric = key.split(':')[0];
      const filter = key.split(':').slice(1).join(':');

      const benchmark = await ctx.db
        .query('benchmarks')
        .withIndex('by_key', (q) =>
          q
            .eq('quarter', args.quarter)
            .eq('metric', metric)
            .eq('filterKey', filter)
        )
        .first();

      if (benchmark) {
        benchmarks.set(key, benchmark.value);
      }
    }
    return Object.fromEntries(benchmarks);
  },
});

// Constants for calculation, duplicating from frontend `instructor.ts`.
// This is technical debt but necessary as backend can't import from frontend.
const PERFORMANCE_WEIGHTS = {
  fillRate: 0.25,
  retention: 0.35,
  satisfaction: 0.3,
  acquisition: 0.1,
};

const STATUS_THRESHOLDS = {
  RISING: 5, // 5% increase
  DECLINING: -5, // 5% decrease
  TOP_PERFORMER: 85,
};

export const getPerformanceDashboardData = query({
  handler: async (ctx) => {
    const instructors = await ctx.db.query('instructors').collect();

    // HACK: This is a temporary fix to avoid the "Too many documents read" error.
    // The previous implementation tried to calculate real metrics on the fly, which was
    // not scalable. This version mocks all performance data on the backend, allowing the
    // UI to render without crashing. The next step is to implement a proper aggregation
    // pipeline (likely using cron jobs) to pre-calculate these metrics.
    const performanceData = instructors.map((instructor) => {
      // --- ALL METRICS ARE CURRENTLY MOCKED ---
      const fillRate = Math.random() * 50 + 50; // Random fill rate between 50% and 100%
      const satisfaction = Math.random() * 2 + 3; // Random rating between 3.0 and 5.0
      const retention = Math.random() * 50 + 50; // Random retention between 50% and 100%
      const acquisition = Math.random() * 20; // Random new client acquisition

      // --- Calculate Weighted Score ---
      const normalizedAcquisition = Math.min((acquisition / 10) * 100, 100);
      const normalizedSatisfaction =
        satisfaction > 0 ? ((satisfaction - 1) / 4) * 100 : 0;

      const score =
        fillRate * PERFORMANCE_WEIGHTS.fillRate +
        retention * PERFORMANCE_WEIGHTS.retention +
        normalizedSatisfaction * PERFORMANCE_WEIGHTS.satisfaction +
        normalizedAcquisition * PERFORMANCE_WEIGHTS.acquisition;
      const performanceScore = Math.max(0, Math.min(score, 100));

      // --- Determine Status & Trend ---
      const monthOverMonthChange = (Math.random() - 0.5) * 15;

      let status: 'top_performer' | 'rising_star' | 'stable' | 'declining' =
        'stable';
      if (
        performanceScore >= STATUS_THRESHOLDS.TOP_PERFORMER &&
        monthOverMonthChange >= 0
      ) {
        status = 'top_performer';
      } else if (monthOverMonthChange > STATUS_THRESHOLDS.RISING) {
        status = 'rising_star';
      } else if (monthOverMonthChange < STATUS_THRESHOLDS.DECLINING) {
        status = 'declining';
      }

      let trend: 'rising' | 'falling' | 'stable' = 'stable';
      if (monthOverMonthChange > 5) trend = 'rising';
      if (monthOverMonthChange < -5) trend = 'falling';

      return {
        instructorId: instructor.instructor_id,
        name: instructor.name,
        retention,
        fillRate,
        satisfaction,
        acquisition,
        performanceScore,
        status,
        trend,
        monthOverMonthChange,
        last30Days: Array.from({ length: 30 }, (_, i) => ({
          date: `Day ${i + 1}`,
          score: performanceScore - 15 + i * Math.random(),
        })),
        eligibilityStatus: { isEligible: true, reason: '' },
      };
    });
    return performanceData;
  },
});
