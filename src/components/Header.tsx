import { UserButton } from '@clerk/clerk-react';
import { useUser } from '@clerk/clerk-react';
import { HouseIcon, ActivityIcon } from '@phosphor-icons/react';

/**
 * Clean Header component with flat design and subtle background
 * Features minimalist styling without gradients or animations
 */
const Header = () => {
  const { user, isLoaded } = useUser();

  return (
    <header className="bg-slate-50 border-b border-slate-200 shadow-sm">
      <div className="flex h-16 items-center justify-between px-6">
        <div className="flex items-center gap-4">
          {/* Clean Logo without glow effects */}
          <img
            src="/the-hb.jpeg"
            alt="The Handle Bar Logo"
            className="h-10 w-10 object-cover rounded-full border border-slate-200"
          />

          {/* Simplified Navigation Title */}
          <div className="flex items-center gap-2 text-slate-700">
            <HouseIcon size={18} weight="duotone" className="text-slate-500" />
            <span className="text-sm font-medium text-slate-600">
              The Handle Bar
            </span>
            <span className="text-slate-400">•</span>
            <ActivityIcon
              size={20}
              weight="duotone"
              className="text-slate-600"
            />
            <span className="text-xl font-bold text-slate-800">
              Performance Hub
            </span>
          </div>
        </div>

        {/* Clean User Section */}
        <div className="flex items-center gap-3">
          {/* Simplified User Status */}
          {isLoaded && user && (
            <div className="flex items-center gap-2 text-slate-600">
              <div className="flex items-center gap-1">
                <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                <span className="text-sm font-medium">Active</span>
              </div>
              <span className="text-slate-400">•</span>
              <span className="text-sm font-medium">
                {user.firstName || 'Manager'}
              </span>
            </div>
          )}

          {/* Clean User Button */}
          <UserButton
            afterSignOutUrl="/"
            appearance={{
              elements: {
                avatarBox:
                  'w-9 h-9 border border-slate-200 hover:border-slate-300 transition-colors duration-200',
              },
            }}
          />
        </div>
      </div>
    </header>
  );
};

export default Header;
