import Header from './Header';

/**
 * Layout component providing the main application structure
 * with Header and main content area.
 * Refactored for new design system with enhanced styling.
 */
const Layout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="min-h-screen flex flex-col bg-background font-sans">
      <Header />
      <main className="flex-1 flex flex-col">{children}</main>
    </div>
  );
};

export default Layout;
