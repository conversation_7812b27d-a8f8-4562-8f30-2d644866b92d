import { Users } from '@phosphor-icons/react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { InstructorPerformance } from '@/types/instructor';

/**
 * ComparisonModal component for side-by-side instructor comparison
 * Refactored with new design system, shadcn/ui components, and new color palette
 */

interface ComparisonModalProps {
  instructors: InstructorPerformance[];
  onClose: () => void;
  isOpen: boolean;
}

export const ComparisonModal = ({
  instructors,
  onClose,
  isOpen,
}: ComparisonModalProps) => {
  const chartColors = ['#4ecdc4', '#1a535c', '#ffe66d', '#ff6b6b'];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="max-w-6xl h-[90vh] overflow-y-auto"
        style={{ fontFamily: 'Montserrat, sans-serif' }}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl font-bold">
            <Users size={24} weight="duotone" className="text-primary" />
            Instructor Comparison
          </DialogTitle>
          <DialogDescription>
            Side-by-side performance comparison of selected instructors
          </DialogDescription>
        </DialogHeader>

        {/* Side-by-side metrics comparison */}
        <Card className="shadow-sm border-border">
          <CardHeader>
            <CardTitle className="text-xl font-semibold">
              Performance Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="font-semibold text-foreground">
                    Metric
                  </TableHead>
                  {instructors.map((inst) => (
                    <TableHead
                      key={inst.instructorId}
                      className="text-center font-semibold text-foreground"
                    >
                      {inst.name}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="font-medium">
                    Performance Score
                  </TableCell>
                  {instructors.map((inst) => (
                    <TableCell
                      key={inst.instructorId}
                      className="text-center font-semibold"
                    >
                      {Math.round(inst.performanceScore || 0)}/100
                    </TableCell>
                  ))}
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Fill Rate</TableCell>
                  {instructors.map((inst) => (
                    <TableCell
                      key={inst.instructorId}
                      className="text-center font-semibold"
                    >
                      {Math.round(inst.fillRate || 0)}%
                    </TableCell>
                  ))}
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">
                    Client Retention
                  </TableCell>
                  {instructors.map((inst) => (
                    <TableCell
                      key={inst.instructorId}
                      className="text-center font-semibold"
                    >
                      {Math.round(inst.retention || 0)}%
                    </TableCell>
                  ))}
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Client Rating</TableCell>
                  {instructors.map((inst) => (
                    <TableCell
                      key={inst.instructorId}
                      className="text-center font-semibold"
                    >
                      {(inst.satisfaction || 0).toFixed(1)}/5
                    </TableCell>
                  ))}
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">
                    New Client Acquisition
                  </TableCell>
                  {instructors.map((inst) => (
                    <TableCell
                      key={inst.instructorId}
                      className="text-center font-semibold"
                    >
                      {Math.round(inst.acquisition || 0)}
                    </TableCell>
                  ))}
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Performance Trends Chart */}
        <Card className="shadow-sm border-border">
          <CardHeader>
            <CardTitle className="text-xl font-semibold">
              30-Day Performance Trends
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart>
                <XAxis
                  dataKey="date"
                  tick={{
                    fontSize: 12,
                    fontFamily: 'Montserrat',
                    fill: '#4a6b70',
                  }}
                  axisLine={{ stroke: '#c7e0e2' }}
                  tickLine={{ stroke: '#c7e0e2' }}
                />
                <YAxis
                  domain={[0, 100]}
                  tick={{
                    fontSize: 12,
                    fontFamily: 'Montserrat',
                    fill: '#4a6b70',
                  }}
                  axisLine={{ stroke: '#c7e0e2' }}
                  tickLine={{ stroke: '#c7e0e2' }}
                />
                <Tooltip />
                <Legend wrapperStyle={{ fontFamily: 'Montserrat' }} />
                {instructors.map((instructor, index) => (
                  <Line
                    key={instructor.instructorId}
                    type="monotone"
                    dataKey="score"
                    data={instructor.last30Days || []}
                    stroke={chartColors[index % chartColors.length]}
                    strokeWidth={2}
                    name={instructor.name}
                    dot={{
                      r: 4,
                      fill: chartColors[index % chartColors.length],
                    }}
                  />
                ))}
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4 pt-4">
          <Button
            variant="outline"
            onClick={onClose}
            className="btn-outline interactive-element"
          >
            Close Comparison
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
