import { useState } from 'react';
import {
  Star,
  Rocket,
  Minus,
  Warning,
  TrendUp,
  TrendDown,
} from '@phosphor-icons/react';
import { type InstructorPerformance } from '../../types/instructor';

// Note: The `payload` prop is provided by Recharts and contains the data item for this bubble.
// biome-ignore lint/suspicious/noExplicitAny: This is a Recharts component prop
type CustomBubbleProps = any & {
  payload: InstructorPerformance;
  compareMode: boolean;
  compareList: InstructorPerformance[];
  onBubbleClick?: (instructor: InstructorPerformance) => void;
};

/**
 * A custom component for rendering interactive bubbles in the Recharts scatter plot.
 * It displays instructor status, trend, and rating, with visual feedback on hover
 * and for different performance states.
 * Refactored with new color palette: Midnight Green, Robin <PERSON>, Naples Yellow, Light Red
 * Enhanced with stronger visual feedback for critical status and improved animations.
 */
export const CustomBubble = (props: CustomBubbleProps) => {
  const { cx, cy, payload, compareMode, compareList, onBubbleClick } = props;
  const [isHovered, setIsHovered] = useState(false);

  // Use a non-linear (cubic) scale for a more pronounced size difference.
  const minRadius = 8;
  const maxRadius = 40;
  const satisfactionScore = payload.satisfaction ?? 1;

  // Normalize score from 1-5 to 0-1
  const normalizedScore = (satisfactionScore - 1) / 4;
  // Apply cubic scaling for exponential growth
  const scaledValue = Math.pow(normalizedScore, 3);
  // Map scaled value to the desired radius range
  const radius = minRadius + scaledValue * (maxRadius - minRadius);

  // Determine colors and icons based on status - New Color Palette with Enhanced Effects
  const getStatusConfig = (status: InstructorPerformance['status']) => {
    switch (status) {
      case 'top_performer':
        return {
          color: '#FFD700', // Gold
          icon: Star,
          glowColor: '#FFF8DC', // Cornsilk
          strokeColor: '#B8860B', // DarkGoldenRod
          glowRadius: 12,
          pulseIntensity: 0.6,
        };
      case 'rising_star':
        return {
          color: '#4ecdc4', // Robin Egg Blue
          icon: Rocket,
          glowColor: '#7dd3c0',
          strokeColor: '#1a535c',
          glowRadius: 10,
          pulseIntensity: 0.4,
        };
      case 'declining':
        return {
          color: '#ff6b6b', // Light Red
          icon: Warning,
          glowColor: '#ff9999',
          strokeColor: '#b91c1c',
          glowRadius: 16, // Enhanced glow radius for critical status
          pulseIntensity: 0.9, // Stronger pulse for urgency
        };
      default:
        return {
          color: '#94a3b8', // Slate 400
          icon: Minus,
          glowColor: '#cbd5e1', // Slate 300
          strokeColor: '#475569', // Slate 600
          glowRadius: 8,
          pulseIntensity: 0.3,
        };
    }
  };

  const config = getStatusConfig(payload.status);
  const Icon = config.icon;
  const TrendIcon =
    payload.trend === 'rising'
      ? TrendUp
      : payload.trend === 'falling'
      ? TrendDown
      : null;

  // Check if selected in compare mode
  const isSelected =
    compareMode &&
    compareList?.some(
      (inst: InstructorPerformance) =>
        inst.instructorId === payload.instructorId
    );

  const isDecining = payload.status === 'declining';
  const shouldPulse = isDecining || payload.status === 'top_performer';
  const isTopPerformer = payload.status === 'top_performer';

  /**
   * Handle bubble click with proper event handling to prevent overlapping bubble issues
   */
  const handleBubbleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onBubbleClick) {
      onBubbleClick(payload);
    }
  };

  return (
    <g
      transform={`translate(${cx}, ${cy})`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleBubbleClick}
      style={{
        cursor: 'pointer',
        zIndex: isHovered
          ? 1000
          : payload.status === 'top_performer'
          ? 100
          : payload.status === 'declining'
          ? 90
          : 10,
        // Ensure proper SVG layering for overlapping bubbles
        pointerEvents: 'all',
      }}
      className="bubble-group"
    >
      {/* SVG Gradient Definitions for Glistening Effect */}
      <defs>
        <radialGradient
          id={`glisten-gradient-${payload.instructorId}`}
          cx="30%"
          cy="30%"
          r="70%"
        >
          <stop offset="0%" stopColor="#FFFACD" stopOpacity="1">
            <animate
              attributeName="stop-opacity"
              values="0.9;1;0.9"
              dur="2.5s"
              repeatCount="indefinite"
            />
          </stop>
          <stop offset="40%" stopColor="#FFD700" stopOpacity="0.95" />
          <stop offset="100%" stopColor="#B8860B" stopOpacity="1" />
        </radialGradient>
        <linearGradient
          id={`shine-gradient-${payload.instructorId}`}
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
          gradientUnits="objectBoundingBox"
        >
          <stop
            offset="0%"
            stopColor="rgba(255, 255, 255, 0.9)"
            stopOpacity="0"
          >
            <animate
              attributeName="stop-opacity"
              values="0;0.9;0"
              dur="3s"
              repeatCount="indefinite"
            />
          </stop>
          <stop offset="50%" stopColor="rgba(255, 255, 255, 1)" stopOpacity="0">
            <animate
              attributeName="stop-opacity"
              values="0;1;0"
              dur="3s"
              repeatCount="indefinite"
              begin="0.3s"
            />
          </stop>
          <stop
            offset="100%"
            stopColor="rgba(255, 255, 255, 0.7)"
            stopOpacity="0"
          >
            <animate
              attributeName="stop-opacity"
              values="0;0.7;0"
              dur="3s"
              repeatCount="indefinite"
              begin="0.6s"
            />
          </stop>
        </linearGradient>
      </defs>

      {/* Enhanced outer glow with stronger effects for critical status */}
      {(shouldPulse || isHovered) && (
        <circle
          r={radius + config.glowRadius}
          fill="none"
          stroke={config.glowColor}
          strokeWidth={isDecining && isHovered ? '4' : '2'}
          opacity={
            isHovered ? config.pulseIntensity : config.pulseIntensity * 0.6
          }
          className={shouldPulse ? 'animate-pulse' : ''}
          style={{
            filter: isDecining
              ? 'drop-shadow(0 0 8px rgba(255, 107, 107, 0.6))'
              : isTopPerformer
              ? 'drop-shadow(0 0 6px rgba(255, 215, 0, 0.4))'
              : 'none',
          }}
        />
      )}

      {/* Additional urgency ring for declining instructors */}
      {isDecining && (
        <circle
          r={radius + config.glowRadius + 6}
          fill="none"
          stroke="#ff6b6b"
          strokeWidth="1"
          opacity={0.4}
          strokeDasharray="4 4"
          className="animate-pulse"
          style={{
            animation: 'pulse 1.5s ease-in-out infinite',
          }}
        />
      )}

      {/* Selection ring in compare mode - using accent color */}
      {isSelected && (
        <circle
          r={radius + 12}
          fill="none"
          stroke="#4ecdc4" // Robin Egg Blue
          strokeWidth="3"
          strokeDasharray="5 5"
          className="animate-spin"
          style={{
            animation: 'spin 3s linear infinite',
          }}
        />
      )}

      {/* Main bubble with enhanced hover effects and gradient for top performers */}
      <circle
        r={radius}
        fill={
          isTopPerformer
            ? `url(#glisten-gradient-${payload.instructorId})`
            : config.color
        }
        fillOpacity={isTopPerformer ? 0.546875 : 0.625}
        stroke={config.strokeColor}
        strokeWidth={isHovered ? (isDecining ? 4 : 3) : 2}
        className="bubble-main"
        style={{
          filter: isHovered
            ? isDecining
              ? 'brightness(1.15) drop-shadow(0 0 12px rgba(255, 107, 107, 0.8))'
              : isTopPerformer
              ? 'brightness(1.1) drop-shadow(0 0 8px rgba(255, 215, 0, 0.6))'
              : 'brightness(1.1) drop-shadow(0 0 6px rgba(78, 205, 196, 0.4))'
            : isDecining
            ? 'drop-shadow(0 0 4px rgba(255, 107, 107, 0.5))'
            : isTopPerformer
            ? 'drop-shadow(0 0 4px rgba(255, 215, 0, 0.3))'
            : 'none',
          transition: 'all 0.2s ease',
          transform: isHovered
            ? isDecining
              ? 'scale(1.08)'
              : 'scale(1.05)'
            : 'scale(1)',
        }}
      />

      {/* Glistening overlay for top performers */}
      {isTopPerformer && (
        <circle
          r={radius}
          fill={`url(#shine-gradient-${payload.instructorId})`}
          fillOpacity="0.625"
          style={{
            mixBlendMode: 'overlay',
          }}
        />
      )}

      {/* Inner circle for contrast with enhanced visibility for critical status */}
      <circle
        r={radius - 4}
        fill={config.color}
        fillOpacity={isDecining ? 0.234375 : 0.15625}
        stroke={config.strokeColor}
        strokeWidth={isDecining ? 1 : 0.5}
        strokeOpacity={isDecining ? 0.390625 : 0.234375}
      />

      {/* Status icon with enhanced styling for critical status */}
      <g transform={`translate(${-12}, ${-12})`}>
        <Icon
          size={isDecining ? 26 : 24}
          weight="duotone"
          color={config.strokeColor}
          style={{
            filter: isDecining
              ? 'drop-shadow(0 1px 2px rgba(185, 28, 28, 0.8))'
              : 'none',
          }}
        />
      </g>

      {/* Trend indicator with enhanced styling */}
      {TrendIcon && (
        <g transform={`translate(${radius - 15}, ${-radius + 5})`}>
          <circle
            r="12"
            fill="#ffffff"
            stroke={config.strokeColor}
            strokeWidth="2"
            style={{
              filter: 'drop-shadow(0 1px 2px rgba(26, 83, 92, 0.15))',
            }}
          />
          <g transform="translate(-8, -8)">
            <TrendIcon
              size={16}
              weight="bold"
              color={payload.trend === 'rising' ? '#4ecdc4' : '#ff6b6b'}
            />
          </g>
        </g>
      )}

      {/* Rating indicator with enhanced styling for critical status */}
      <g transform={`translate(0, ${radius + 10})`}>
        <text
          textAnchor="middle"
          fill={config.strokeColor}
          fontSize={isDecining ? '13' : '12'}
          fontWeight={isDecining ? '700' : '600'}
          fontFamily="Montserrat, sans-serif"
          style={{
            filter: isDecining
              ? 'drop-shadow(0 1px 1px rgba(185, 28, 28, 0.6))'
              : 'none',
          }}
        >
          {(payload.satisfaction ?? 0).toFixed(1)}★
        </text>
      </g>

      {/* Critical status indicator */}
      {isDecining && (
        <g transform={`translate(${-radius + 8}, ${-radius + 8})`}>
          <circle
            r="6"
            fill="#ff6b6b"
            stroke="#ffffff"
            strokeWidth="2"
            className="animate-pulse"
            style={{
              filter: 'drop-shadow(0 1px 3px rgba(255, 107, 107, 0.8))',
            }}
          />
          <text
            textAnchor="middle"
            dominantBaseline="middle"
            fill="#ffffff"
            fontSize="8"
            fontWeight="900"
          >
            !
          </text>
        </g>
      )}
    </g>
  );
};
