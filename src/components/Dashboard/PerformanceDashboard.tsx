import { useState } from 'react';
import { Dialog } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { usePreparedData } from '@/hooks/usePreparedData';
import PerformanceGalaxy from './PerformanceGalaxy';
import { InstructorDetailModal } from './InstructorDetailModal';
import { StatusBar } from './StatusBar';
import { PerformanceGalaxyHeader } from './PerformanceGalaxyHeader';
import { ChartBar, TrendUp } from '@phosphor-icons/react';
import type { InstructorPerformance } from '@/types/instructor';
import { Button } from '@/components/ui/button';
import {
  Dialog as ExplanationDialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Info, UserCheck, UserPlus, UsersRound, Smile } from 'lucide-react';

/**
 * A dashboard component to visualize instructor performance data.
 * This is the main component that orchestrates the entire dashboard.
 * Refactored for new design system with Montserrat font and new color palette.
 */
const PerformanceDashboard = () => {
  const [selectedInstructor, setSelectedInstructor] =
    useState<InstructorPerformance | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [compareList, setCompareList] = useState<InstructorPerformance[]>([]);
  const preparedData = usePreparedData();

  const handleBubbleClick = (instructor: InstructorPerformance) => {
    setSelectedInstructor(instructor);
    setIsModalOpen(true);
  };

  if (!preparedData || preparedData.length === 0) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="loading-shimmer h-12 w-12 rounded-full"></div>
          <p className="text-muted-foreground">Loading performance data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col gap-6 p-6">
      <header className="flex-shrink-0">
        <div className="flex items-center justify-between gap-3 mb-2">
          <div className="flex items-center gap-3">
            <TrendUp size={32} weight="duotone" className="text-primary" />
            <h1 className="text-3xl font-bold text-foreground">
              Performance Dashboard
            </h1>
          </div>
          <ExplanationDialog>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                className="flex items-center gap-2"
                size="sm"
              >
                <Info size={16} />
                How is Performance Calculated?
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-3 text-2xl">
                  <Info size={24} className="text-primary" />
                  Understanding Instructor Performance
                </DialogTitle>
                <DialogDescription>
                  Our performance score is a comprehensive metric designed to
                  provide a holistic view of an instructor's impact. Here's how
                  it's calculated.
                </DialogDescription>
              </DialogHeader>

              <div className="grid gap-8 py-4">
                <div className="space-y-3">
                  <h3 className="font-semibold text-lg">
                    Weighted Performance Score Breakdown
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    The final score is a weighted average of four key metrics.
                    This model emphasizes long-term client value over simple
                    attendance numbers.
                  </p>
                  <div className="flex h-8 w-full rounded-md overflow-hidden border">
                    <div
                      className="flex items-center justify-center bg-sky-500"
                      style={{ width: '35%' }}
                      title="Client Retention: 35%"
                    >
                      <span className="text-white text-xs font-bold">
                        Retention
                      </span>
                    </div>
                    <div
                      className="flex items-center justify-center bg-teal-500"
                      style={{ width: '30%' }}
                      title="Satisfaction: 30%"
                    >
                      <span className="text-white text-xs font-bold">
                        Satisfaction
                      </span>
                    </div>
                    <div
                      className="flex items-center justify-center bg-indigo-500"
                      style={{ width: '25%' }}
                      title="Fill Rate: 25%"
                    >
                      <span className="text-white text-xs font-bold">
                        Fill Rate
                      </span>
                    </div>
                    <div
                      className="flex items-center justify-center bg-amber-500"
                      style={{ width: '10%' }}
                      title="New Client Acquisition: 10%"
                    >
                      <span className="text-white text-xs font-bold">
                        Acquisition
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="font-semibold text-lg">
                    Core Metrics Explained
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between pb-2">
                        <CardTitle className="text-sm font-medium">
                          Client Retention
                        </CardTitle>
                        <UserCheck className="h-4 w-4 text-muted-foreground" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-sky-500">
                          35%
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Measures an instructor's ability to create a loyal
                          following. It's calculated by the percentage of
                          clients who return specifically to that instructor's
                          classes within 30 days. This period is the industry
                          standard for measuring immediate engagement, but can
                          be adjusted to track long-term loyalty.
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between pb-2">
                        <CardTitle className="text-sm font-medium">
                          Client Satisfaction
                        </CardTitle>
                        <Smile className="h-4 w-4 text-muted-foreground" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-teal-500">
                          30%
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Reflects direct client feedback. This score is the
                          average rating (1-5 scale) from post-class surveys,
                          which are automatically sent and tracked via FitGrid.
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between pb-2">
                        <CardTitle className="text-sm font-medium">
                          Fill Rate
                        </CardTitle>
                        <UsersRound className="h-4 w-4 text-muted-foreground" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-indigo-500">
                          25%
                        </div>
                        <p className="text-xs text-muted-foreground">
                          This isn't a raw average. To ensure fairness, Fill
                          Rate is normalized against the studio's historical
                          average for that specific class type, time slot, and
                          location. This prevents penalizing instructors for
                          off-peak classes.
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between pb-2">
                        <CardTitle className="text-sm font-medium">
                          New Client Conversion
                        </CardTitle>
                        <UserPlus className="h-4 w-4 text-muted-foreground" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-amber-500">
                          10%
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Measures the instructor's impact on first-time
                          visitors. It's the percentage of new clients who take
                          a class with this instructor and return to the studio
                          for any class within 30 days.
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="font-semibold text-lg">
                    Performance Zones Explained
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    The Performance Galaxy scatter plot is divided into four
                    zones to help you quickly identify performance patterns
                    based on Fill Rate (horizontal axis) and Retention (vertical
                    axis).
                  </p>
                  <div className="grid grid-cols-2 gap-x-8 gap-y-4">
                    <div className="flex items-start gap-3">
                      <div className="w-3 h-3 mt-1 rounded-full bg-[#0284c7]"></div>
                      <div>
                        <h4 className="font-medium">Star Zone</h4>
                        <p className="text-sm text-muted-foreground">
                          High Fill Rate & High Retention. These are your
                          top-tier instructors who consistently deliver
                          excellence.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-3 h-3 mt-1 rounded-full bg-[#f59e0b]"></div>
                      <div>
                        <h4 className="font-medium">Boost Fill Rate</h4>
                        <p className="text-sm text-muted-foreground">
                          Low Fill Rate & High Retention. Clients love them;
                          focus on marketing and optimal scheduling.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-3 h-3 mt-1 rounded-full bg-[#e11d48]"></div>
                      <div>
                        <h4 className="font-medium">Improve Retention</h4>
                        <p className="text-sm text-muted-foreground">
                          High Fill Rate & Low Retention. Popular but need to
                          focus on building long-term client relationships.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-3 h-3 mt-1 rounded-full bg-[#78716c]"></div>
                      <div>
                        <h4 className="font-medium">Needs Support</h4>
                        <p className="text-sm text-muted-foreground">
                          Low Fill Rate & Low Retention. These instructors may
                          benefit from additional coaching and development.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="font-semibold text-lg">
                    How to Read the Bubble Chart
                  </h3>
                  <div className="bg-muted/50 p-4 rounded-lg">
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-primary"></div>
                        <strong>X-Axis (Fill Rate):</strong> How well classes
                        are attended
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-primary"></div>
                        <strong>Y-Axis (Retention):</strong> How well clients
                        return for more classes
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-primary"></div>
                        <strong>Bubble Size:</strong> Represents satisfaction
                        rating (larger = higher satisfaction)
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-primary"></div>
                        <strong>Bubble Color:</strong> Performance status (blue
                        = top performer, red = declining, etc.)
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </DialogContent>
          </ExplanationDialog>
        </div>
        <p className="text-muted-foreground">
          Visualize and analyze instructor performance metrics across key
          indicators
        </p>
      </header>

      <div className="grid grid-cols-3 gap-6">
        <div className="col-span-3">
          <StatusBar
            data={preparedData}
            onInstructorSelect={handleBubbleClick}
          />
        </div>

        <main className="col-span-3">
          <Card className="h-full shadow-sm border-border">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2 text-lg font-semibold">
                <ChartBar size={20} weight="duotone" className="text-primary" />
                Performance Galaxy
              </CardTitle>
              <PerformanceGalaxyHeader />
            </CardHeader>
            <CardContent className="px-4 h-[calc(100vh-220px)]">
              <PerformanceGalaxy
                instructors={preparedData}
                onInstructorSelect={handleBubbleClick}
                compareList={compareList}
                onCompareListChange={setCompareList}
              />
            </CardContent>
          </Card>
        </main>
      </div>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        {selectedInstructor && (
          <InstructorDetailModal
            instructor={selectedInstructor}
            onCompare={() => {
              // Placeholder for comparison logic
              console.log('Compare clicked for:', selectedInstructor.name);
            }}
          />
        )}
      </Dialog>
    </div>
  );
};

export default PerformanceDashboard;
