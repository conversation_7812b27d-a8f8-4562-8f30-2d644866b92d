import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>A<PERSON><PERSON>,
  YA<PERSON>s,
  ZAxis,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine,
  ReferenceArea,
} from 'recharts';
import { InfoIcon } from '@phosphor-icons/react';
import { type InstructorPerformance } from '../../types/instructor';
import { CustomBubble } from './CustomBubble';
import { CustomTooltip } from './CustomTooltip';

/**
 * Performance Galaxy scatter plot component
 * Visualizes instructor performance across fill rate and retention metrics
 * Uses new color palette with Midnight Green, Robin Egg Blue, etc.
 * Enhanced with improved performance zone visibility and context.
 */

interface PerformanceGalaxyProps {
  instructors: InstructorPerformance[];
  onInstructorSelect: (instructor: InstructorPerformance) => void;
  compareList: InstructorPerformance[];
  onCompareListChange: (list: InstructorPerformance[]) => void;
}

const PerformanceGalaxy = ({
  instructors,
  onInstructorSelect,
  compareList,
  onCompareListChange,
}: PerformanceGalaxyProps) => {
  // Transform data for scatter plot
  const scatterData = instructors.map((inst) => ({
    x: inst.fillRate,
    y: inst.retention,
    z: inst.satisfaction, // This controls bubble size
    ...inst,
  }));

  // Studio averages
  const avgFillRate =
    instructors.reduce((sum, inst) => sum + inst.fillRate, 0) /
      (instructors.length || 1) || 50;
  const avgRetention =
    instructors.reduce((sum, inst) => sum + inst.retention, 0) /
      (instructors.length || 1) || 50;

  // Enhanced performance zones with colors matching the dialog explanation
  const performanceZones = [
    {
      x1: 0,
      x2: avgFillRate,
      y1: 0,
      y2: avgRetention,
      fill: 'rgba(120, 113, 108, 0.12)', // Gray - Needs Support
      stroke: '#78716c',
      strokeWidth: 1,
      strokeOpacity: 0.3,
      label: 'Needs Support',
    },
    {
      x1: avgFillRate,
      x2: 100,
      y1: 0,
      y2: avgRetention,
      fill: 'rgba(225, 29, 72, 0.1)', // Red - Improve Retention
      stroke: '#e11d48',
      strokeWidth: 0.5,
      strokeOpacity: 0.2,
      label: 'Improve Retention',
    },
    {
      x1: 0,
      x2: avgFillRate,
      y1: avgRetention,
      y2: 100,
      fill: 'rgba(245, 158, 11, 0.1)', // Amber - Boost Fill Rate
      stroke: '#f59e0b',
      strokeWidth: 0.5,
      strokeOpacity: 0.2,
      label: 'Boost Fill Rate',
    },
    {
      x1: avgFillRate,
      x2: 100,
      y1: avgRetention,
      y2: 100,
      fill: 'rgba(2, 132, 199, 0.1)', // Blue - Star Zone
      stroke: '#0284c7',
      strokeWidth: 0.5,
      strokeOpacity: 0.2,
      label: 'Star Zone',
    },
  ];

  // biome-ignore lint/suspicious/noExplicitAny: This is a Recharts event handler
  const handleBubbleClick = (data: any) => {
    if (compareList.length > 0) {
      // In compare mode
      const isSelected = compareList.some(
        (inst) => inst.instructorId === data.instructorId
      );
      if (isSelected) {
        onCompareListChange(
          compareList.filter((inst) => inst.instructorId !== data.instructorId)
        );
      } else if (compareList.length < 4) {
        onCompareListChange([...compareList, data]);
      }
    } else {
      // Normal selection mode
      onInstructorSelect(data);
    }
  };

  return (
    <div className="flex-grow flex flex-col bg-card h-full">
      <div className="flex-grow w-full h-full">
        <ResponsiveContainer width="100%" height="100%">
          <ScatterChart
            margin={{ top: 10, right: 10, bottom: 20, left: 10 }}
            className="chart-element"
            style={{ overflow: 'visible' }}
          >
            {/* Enhanced background zones with improved visibility */}
            {performanceZones.map((zone) => (
              <ReferenceArea
                key={zone.label}
                x1={zone.x1}
                x2={zone.x2}
                y1={zone.y1}
                y2={zone.y2}
                fill={zone.fill}
                stroke={zone.stroke}
                strokeWidth={zone.strokeWidth}
                strokeOpacity={zone.strokeOpacity}
                label={{
                  position: 'inside',
                  value: zone.label,
                  fill: zone.stroke,
                  fontSize: '24px',
                  fontWeight: 'bold',
                  opacity: 0.4,
                }}
              />
            ))}

            {/* Enhanced studio average lines with Robin Egg Blue styling */}
            <ReferenceLine
              x={avgFillRate}
              stroke="#4ecdc4" // Robin Egg Blue for enhanced reference lines
              strokeDasharray="8 4"
              strokeWidth={2}
              opacity={0.8}
            />
            <ReferenceLine
              y={avgRetention}
              stroke="#4ecdc4" // Robin Egg Blue for enhanced reference lines
              strokeDasharray="8 4"
              strokeWidth={2}
              opacity={0.8}
            />

            {/* Axes - updated styling */}
            <XAxis
              type="number"
              dataKey="x"
              name="Fill Rate"
              unit="%"
              domain={[-5, 105]}
              label={{
                value: 'Fill Rate →',
                position: 'insideBottom',
                offset: -10,
                className: 'text-muted-foreground font-medium text-sm',
              }}
              tick={{ fill: '#4a6b70', fontSize: 12, fontFamily: 'Montserrat' }}
              axisLine={{ stroke: '#c7e0e2' }}
              tickLine={{ stroke: '#c7e0e2' }}
            />
            <YAxis
              type="number"
              dataKey="y"
              name="Client Retention"
              unit="%"
              domain={[-5, 105]}
              label={{
                value: 'Client Retention',
                angle: -90,
                position: 'insideLeft',
                offset: -5,
                className: 'text-muted-foreground font-medium text-sm',
              }}
              tick={{ fill: '#4a6b70', fontSize: 12, fontFamily: 'Montserrat' }}
              axisLine={{ stroke: '#c7e0e2' }}
              tickLine={{ stroke: '#c7e0e2' }}
            />
            <ZAxis
              type="number"
              dataKey="z"
              domain={[1, 5]}
              range={[100, 1000]} // Bubble size range based on rating
              name="Rating"
            />

            <Tooltip
              content={<CustomTooltip />}
              cursor={{ strokeDasharray: '3 3', stroke: '#4ecdc4' }}
            />

            <Scatter
              name="Instructors"
              data={scatterData}
              shape={
                <CustomBubble
                  compareMode={compareList.length > 0}
                  compareList={compareList}
                  onBubbleClick={handleBubbleClick}
                />
              }
            />
          </ScatterChart>
        </ResponsiveContainer>
      </div>

      {compareList.length > 0 && (
        <div className="flex items-center justify-center gap-3 p-2 bg-secondary text-secondary-foreground rounded-lg border border-border interactive-element">
          <InfoIcon size={16} weight="duotone" className="text-accent" />
          <span className="font-medium text-sm">
            Select up to 4 instructors to compare ({compareList.length}/4)
          </span>
        </div>
      )}

      {/* Help text for overlapping bubbles */}
      <div className="flex items-center justify-center gap-2 p-1 text-xs text-muted-foreground">
        <InfoIcon
          size={12}
          weight="duotone"
          className="text-muted-foreground"
        />
        <span>
          Hover over bubbles to bring them to front • Larger bubbles indicate
          higher satisfaction ratings
        </span>
      </div>
    </div>
  );
};

export default PerformanceGalaxy;
