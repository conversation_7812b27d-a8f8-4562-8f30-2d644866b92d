import { useMemo } from 'react';
// We no longer need createPortal
// import { createPortal } from 'react-dom';
import {
  ResponsiveContainer,
  RadialBarChart,
  RadialBar,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  AreaChart,
  Area,
} from 'recharts';
import {
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Star,
  Rocket,
  Equals,
  Warning,
  Users,
  ChartLine,
  TrendUp,
  UserPlus,
  ClipboardText,
} from '@phosphor-icons/react';
import { type InstructorPerformance } from '../../types/instructor';

/**
 * Instructor Detail Modal - Refactored with new design system
 * Uses new color palette: Midnight Green, <PERSON>, Naples Yellow, <PERSON> Red
 * Enhanced with shadcn/ui components and Montserrat font
 * Enhanced with action orientation for declining instructors.
 */

/** --- Stubs for Helper Functions --- */

// biome-ignore lint/suspicious/noExplicitAny: This is a mock data generator
const generateWeeklyPattern = (_instructor: any) => [
  { day: 'Mon', morning: 0, afternoon: 0, evening: 0 },
  { day: 'Tue', morning: 80, afternoon: 90, evening: 75 },
  { day: 'Wed', morning: 85, afternoon: 0, evening: 0 },
  { day: 'Thu', morning: 70, afternoon: 80, evening: 65 },
  { day: 'Fri', morning: 90, afternoon: 0, evening: 88 },
  { day: 'Sat', morning: 95, afternoon: 92, evening: 0 },
  { day: 'Sun', morning: 88, afternoon: 85, evening: 0 },
];

const getStatusIcon = (status: InstructorPerformance['status']) => {
  const iconProps = { size: 16, weight: 'duotone' as const };
  switch (status) {
    case 'top_performer':
      return <Star {...iconProps} className="text-[#8b6914]" />;
    case 'rising_star':
      return <Rocket {...iconProps} className="text-[#1a535c]" />;
    case 'declining':
      return <Warning {...iconProps} className="text-[#b91c1c]" />;
    default:
      return <Equals {...iconProps} className="text-[#2a5a61]" />;
  }
};

const formatStatus = (status: InstructorPerformance['status']) => {
  return status.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase());
};

/*
// biome-ignore lint/suspicious/noExplicitAny: This is a mock data helper
const getBestTimeSlot = (weeklyData: any) => 'Saturday Morning';

// biome-ignore lint/suspicious/noExplicitAny: This is a mock data helper
const getRetentionComparison = (instructor: any) => 1.2;
*/

/** --- Component Implementation --- */

interface InstructorDetailModalProps {
  instructor: InstructorPerformance;
  onCompare: () => void;
  // The `onClose` prop is no longer needed as shadcn/ui handles it
}

export const InstructorDetailModal = ({
  instructor,
  onCompare,
}: InstructorDetailModalProps) => {
  // Prepare weekly pattern data using a memoized stub
  const weeklyData = useMemo(
    () => generateWeeklyPattern(instructor),
    [instructor]
  );

  const isDecining = instructor.status === 'declining';
  const needsCriticalAttention =
    isDecining ||
    (instructor.performanceScore ?? 0) < 60 ||
    (instructor.fillRate ?? 0) < 50 ||
    (instructor.retention ?? 0) < 60;

  return (
    <DialogContent
      className={`bg-background h-[90vh] max-w-5xl overflow-y-auto ${
        isDecining ? 'border-2 border-[#ff6b6b]' : ''
      }`}
      style={{
        fontFamily: 'Montserrat, sans-serif',
        backgroundColor: isDecining ? '#fff5f5' : '#ffffff',
        opacity: 1,
        boxShadow: isDecining
          ? '0 25px 50px rgba(255, 107, 107, 0.3)'
          : '0 25px 50px rgba(0, 0, 0, 0.1)',
      }}
    >
      <DialogHeader>
        <DialogTitle className="sr-only">{instructor.name} Details</DialogTitle>
        <DialogDescription className="sr-only">
          Detailed performance metrics for {instructor.name}.
        </DialogDescription>
      </DialogHeader>

      {/* Enhanced Header with Action-Oriented Design */}
      <div
        className={`mb-6 flex items-start justify-between border-b pb-6 ${
          isDecining ? 'border-[#ff6b6b]/30' : 'border-border'
        }`}
      >
        <div>
          <div className="flex items-center gap-3">
            <h2 className="text-3xl font-bold text-foreground">
              {instructor.name}
            </h2>
            {isDecining && (
              <div className="flex items-center gap-2 px-3 py-1 bg-[#ff6b6b] text-white rounded-full animate-pulse">
                <Warning size={16} weight="duotone" />
                <span className="text-sm font-bold">ACTION REQUIRED</span>
              </div>
            )}
          </div>
          <p className="mt-1 text-base text-muted-foreground">
            {isDecining
              ? 'Performance Analysis & Improvement Plan'
              : 'Performance Analysis & Insights'}
          </p>
          {needsCriticalAttention && (
            <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-start gap-2">
                <Warning
                  size={16}
                  weight="duotone"
                  className="text-[#b91c1c] mt-0.5"
                />
                <div>
                  <p className="text-sm font-medium text-[#b91c1c]">
                    Immediate Attention Needed
                  </p>
                  <p className="text-xs text-[#b91c1c] mt-1">
                    This instructor requires coaching and support to improve
                    performance.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="flex flex-col items-end gap-3">
          <div className="flex gap-3">
            <span
              className={`flex items-center gap-2 rounded-full px-4 py-2 text-sm font-medium border status-${
                instructor.status
              } ${
                isDecining
                  ? 'animate-pulse ring-2 ring-[#ff6b6b] ring-opacity-50'
                  : ''
              }`}
              style={{
                backgroundColor: isDecining ? '#ff6b6b' : undefined,
                color: isDecining ? '#ffffff' : undefined,
                borderColor: isDecining ? '#ff6b6b' : undefined,
              }}
            >
              {getStatusIcon(instructor.status)}
              {formatStatus(instructor.status)}
            </span>
            <span
              className={`flex items-center rounded-full px-4 py-2 text-sm font-medium border border-border ${
                (instructor.performanceScore ?? 0) < 60
                  ? 'bg-red-100 text-[#b91c1c] border-red-200'
                  : 'bg-secondary text-secondary-foreground'
              }`}
            >
              Score: {Math.round(instructor.performanceScore ?? 0)}/100
            </span>
          </div>
          <div className="flex gap-2">
            {isDecining && (
              <Button
                className="flex items-center gap-2 bg-[#ff6b6b] hover:bg-[#e55555] text-white animate-pulse"
                size="sm"
              >
                <ClipboardText size={16} weight="duotone" />
                Create Action Plan
              </Button>
            )}
            <Button
              onClick={onCompare}
              className="flex items-center gap-2 interactive-element"
              variant={isDecining ? 'outline' : 'default'}
              size="sm"
            >
              <Users size={16} weight="duotone" />
              Compare Instructor
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Key Metrics Grid with Critical Status Indicators */}
      <div className="mb-8 grid grid-cols-4 gap-6">
        {/* Fill Rate */}
        <Card
          className={`text-center shadow-sm ${
            (instructor.fillRate ?? 0) < 50
              ? 'border-red-200 bg-red-50'
              : 'border-border'
          }`}
        >
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-center gap-2 text-sm">
              {(instructor.fillRate ?? 0) < 50 ? (
                <Warning
                  size={20}
                  weight="duotone"
                  className="text-[#ff6b6b]"
                />
              ) : (
                <ChartLine
                  size={20}
                  weight="duotone"
                  className="text-[#4ecdc4]"
                />
              )}
              Fill Rate
              {(instructor.fillRate ?? 0) < 50 && (
                <span className="ml-1 text-xs text-[#ff6b6b] font-bold">
                  ⚠️
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={100}>
              <RadialBarChart
                cx="50%"
                cy="50%"
                innerRadius="65%"
                outerRadius="85%"
                data={[
                  {
                    value: instructor.fillRate ?? 0,
                    fill:
                      (instructor.fillRate ?? 0) < 50 ? '#ff6b6b' : '#4ecdc4',
                  },
                ]}
                startAngle={90}
                endAngle={-270}
              >
                <RadialBar
                  dataKey="value"
                  cornerRadius={10}
                  background={{ fill: '#e8f4f5' }}
                />
                <text
                  x="50%"
                  y="50%"
                  textAnchor="middle"
                  dominantBaseline="middle"
                  className={`text-2xl font-bold ${
                    (instructor.fillRate ?? 0) < 50
                      ? 'fill-[#ff6b6b]'
                      : 'fill-foreground'
                  }`}
                  style={{ fontFamily: 'Montserrat, sans-serif' }}
                >
                  {(instructor.fillRate ?? 0).toFixed(0)}%
                </text>
              </RadialBarChart>
            </ResponsiveContainer>
            <p
              className={`mt-2 text-xs ${
                (instructor.fillRate ?? 0) < 50
                  ? 'text-[#b91c1c]'
                  : 'text-muted-foreground'
              }`}
            >
              {(instructor.fillRate ?? 0) < 50
                ? 'Below target - needs improvement'
                : 'Avg class capacity utilization'}
            </p>
          </CardContent>
        </Card>

        {/* Client Retention */}
        <Card
          className={`text-center shadow-sm ${
            (instructor.retention ?? 0) < 60
              ? 'border-red-200 bg-red-50'
              : 'border-border'
          }`}
        >
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-center gap-2 text-sm">
              {(instructor.retention ?? 0) < 60 ? (
                <Warning
                  size={20}
                  weight="duotone"
                  className="text-[#ff6b6b]"
                />
              ) : (
                <Users size={20} weight="duotone" className="text-[#7dd3c0]" />
              )}
              Client Retention
              {(instructor.retention ?? 0) < 60 && (
                <span className="ml-1 text-xs text-[#ff6b6b] font-bold">
                  ⚠️
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={100}>
              <RadialBarChart
                cx="50%"
                cy="50%"
                innerRadius="65%"
                outerRadius="85%"
                data={[
                  {
                    value: instructor.retention ?? 0,
                    fill:
                      (instructor.retention ?? 0) < 60 ? '#ff6b6b' : '#7dd3c0',
                  },
                ]}
                startAngle={90}
                endAngle={-270}
              >
                <RadialBar
                  dataKey="value"
                  cornerRadius={10}
                  background={{ fill: '#e8f4f5' }}
                />
                <text
                  x="50%"
                  y="50%"
                  textAnchor="middle"
                  dominantBaseline="middle"
                  className={`text-2xl font-bold ${
                    (instructor.retention ?? 0) < 60
                      ? 'fill-[#ff6b6b]'
                      : 'fill-foreground'
                  }`}
                  style={{ fontFamily: 'Montserrat, sans-serif' }}
                >
                  {(instructor.retention ?? 0).toFixed(0)}%
                </text>
              </RadialBarChart>
            </ResponsiveContainer>
            <p
              className={`mt-2 text-xs ${
                (instructor.retention ?? 0) < 60
                  ? 'text-[#b91c1c]'
                  : 'text-muted-foreground'
              }`}
            >
              {(instructor.retention ?? 0) < 60
                ? 'Below target - focus on engagement'
                : 'New clients returning within quarter'}
            </p>
          </CardContent>
        </Card>

        {/* Client Satisfaction */}
        <Card
          className={`text-center shadow-sm ${
            (instructor.satisfaction ?? 0) < 3.5
              ? 'border-red-200 bg-red-50'
              : 'border-border'
          }`}
        >
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-center gap-2 text-sm">
              {(instructor.satisfaction ?? 0) < 3.5 ? (
                <Warning
                  size={20}
                  weight="duotone"
                  className="text-[#ff6b6b]"
                />
              ) : (
                <Star size={20} weight="duotone" className="text-[#ffe66d]" />
              )}
              Client Rating
              {(instructor.satisfaction ?? 0) < 3.5 && (
                <span className="ml-1 text-xs text-[#ff6b6b] font-bold">
                  ⚠️
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex h-[100px] flex-col items-center justify-center">
              <div className="flex items-baseline">
                <span
                  className={`text-4xl font-bold ${
                    (instructor.satisfaction ?? 0) < 3.5
                      ? 'text-[#ff6b6b]'
                      : 'text-foreground'
                  }`}
                >
                  {(instructor.satisfaction ?? 0).toFixed(1)}
                </span>
                <span className="text-lg text-muted-foreground">/5</span>
              </div>
              <div className="mt-2 flex">
                {Array.from({ length: 5 }, (_, i) => (
                  <Star
                    key={i}
                    size={20}
                    weight={
                      i < Math.round(instructor.satisfaction ?? 0)
                        ? 'fill'
                        : 'light'
                    }
                    className={
                      (instructor.satisfaction ?? 0) < 3.5
                        ? 'text-[#ff6b6b]'
                        : 'text-[#ffe66d]'
                    }
                  />
                ))}
              </div>
            </div>
            <p
              className={`mt-2 text-xs ${
                (instructor.satisfaction ?? 0) < 3.5
                  ? 'text-[#b91c1c]'
                  : 'text-muted-foreground'
              }`}
            >
              {(instructor.satisfaction ?? 0) < 3.5
                ? 'Below target - review teaching style'
                : 'Avg. satisfaction score from clients'}
            </p>
          </CardContent>
        </Card>

        {/* New Client Acquisition */}
        <Card className="text-center shadow-sm border-border">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-center gap-2 text-sm">
              <UserPlus size={20} weight="duotone" className="text-[#ff6b6b]" />
              New Clients
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex h-[100px] flex-col items-center justify-center">
              <span className="text-4xl font-bold text-foreground">
                {Math.round(instructor.acquisition ?? 0)}
              </span>
            </div>
            <p className="mt-2 text-xs text-muted-foreground">
              New clients in the last 90 days
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts section */}
      <div className="grid grid-cols-2 gap-6">
        {/* Trend Analysis */}
        <Card className="col-span-1 shadow-sm border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <TrendUp size={20} weight="duotone" className="text-primary" />
              Performance Trend
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={instructor.last30Days}>
                  <defs>
                    <linearGradient
                      id="chart-gradient"
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="5%" stopColor="#4ecdc4" stopOpacity={0.4} />
                      <stop offset="95%" stopColor="#4ecdc4" stopOpacity={0} />
                    </linearGradient>
                  </defs>
                  <XAxis
                    dataKey="date"
                    tickFormatter={(val) => val.replace('Day ', '')}
                    tick={{
                      fill: '#4a6b70',
                      fontSize: 12,
                      fontFamily: 'Montserrat',
                    }}
                    axisLine={{ stroke: '#c7e0e2' }}
                    tickLine={{ stroke: '#c7e0e2' }}
                  />
                  <YAxis
                    domain={[0, 100]}
                    tick={{
                      fill: '#4a6b70',
                      fontSize: 12,
                      fontFamily: 'Montserrat',
                    }}
                    axisLine={{ stroke: '#c7e0e2' }}
                    tickLine={{ stroke: '#c7e0e2' }}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(255, 255, 255, 0.9)',
                      border: '1px solid #c7e0e2',
                      borderRadius: '0.5rem',
                      fontFamily: 'Montserrat, sans-serif',
                    }}
                  />
                  <Legend wrapperStyle={{ fontSize: '14px' }} />
                  <Area
                    type="monotone"
                    dataKey="score"
                    name="Performance Score"
                    stroke="#1a535c"
                    fill="url(#chart-gradient)"
                    strokeWidth={2}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4 flex items-center justify-center gap-4 text-center">
              <div className="text-sm">
                <p className="text-muted-foreground">30d Avg.</p>
                <p className="font-semibold text-foreground">
                  {(
                    instructor.last30Days.reduce(
                      (sum, day) => sum + day.score,
                      0
                    ) / (instructor.last30Days.length || 1)
                  ).toFixed(1)}
                </p>
              </div>
              <div className="text-sm">
                <p className="text-muted-foreground">MoM Change</p>
                <p
                  className={`font-semibold ${
                    (instructor.monthOverMonthChange ?? 0) >= 0
                      ? 'text-primary'
                      : 'text-[#ff6b6b]'
                  }`}
                >
                  {(instructor.monthOverMonthChange ?? 0) > 0 ? '+' : ''}
                  {(instructor.monthOverMonthChange ?? 0).toFixed(1)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Class Pattern */}
        <Card className="col-span-1 shadow-sm border-border">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <ChartLine
                size={20}
                weight="duotone"
                className="text-[#4ecdc4]"
              />
              Class Pattern
            </CardTitle>
          </CardHeader>
          <CardContent className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={weeklyData}>
                <XAxis
                  dataKey="day"
                  tick={{
                    fontSize: 12,
                    fontFamily: 'Montserrat',
                    fill: '#4a6b70',
                  }}
                  axisLine={{ stroke: '#c7e0e2' }}
                  tickLine={{ stroke: '#c7e0e2' }}
                />
                <YAxis
                  tick={{
                    fontSize: 12,
                    fontFamily: 'Montserrat',
                    fill: '#4a6b70',
                  }}
                  axisLine={{ stroke: '#c7e0e2' }}
                  tickLine={{ stroke: '#c7e0e2' }}
                />
                <Tooltip />
                <Legend
                  wrapperStyle={{ fontSize: '14px', fontFamily: 'Montserrat' }}
                />
                <Bar
                  dataKey="morning"
                  stackId="a"
                  fill="#8884d8"
                  name="Morning"
                />
                <Bar
                  dataKey="afternoon"
                  stackId="a"
                  fill="#82ca9d"
                  name="Afternoon"
                />
                <Bar
                  dataKey="evening"
                  stackId="a"
                  fill="#ffc658"
                  name="Evening"
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </DialogContent>
  );
};
