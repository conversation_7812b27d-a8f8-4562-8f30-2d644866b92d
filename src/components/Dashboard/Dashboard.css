.tooltip-footer {
  margin-top: 0.75rem;
  padding-top: 0.5rem;
  border-top: 1px solid #3a3a5a;
  font-size: 0.75rem;
  color: #9c9cff;
  text-align: center;
  opacity: 0.8;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(10, 10, 20, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #1a1a2e;
  border: 1px solid #5a5a78;
  border-radius: 12px;
  padding: 1.5rem;
  color: #e0e0fc;
  position: relative;
  max-width: 90vw;
  width: 600px;
}

.modal-content.comparison-modal {
  width: 800px;
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: #9c9cff;
  cursor: pointer;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #3a3a5a;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
}

.detail-header h2,
.comparison-modal h2 {
  margin: 0;
  font-size: 1.5rem;
}

.compare-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #3a3a5a;
  border: 1px solid #5a5a78;
  color: #e0e0fc;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.compare-button:hover {
  background-color: #5a5a78;
}

/* Comparison Modal Table */
.metrics-comparison,
.trend-comparison {
  margin-top: 1.5rem;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  text-align: left;
}

.comparison-table th,
.comparison-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #3a3a5a;
}

.comparison-table th {
  color: #9c9cff;
  font-weight: normal;
}

.comparison-table td {
  font-size: 1.125rem;
  font-weight: bold;
  text-align: center;
}

.comparison-table th:first-child,
.comparison-table td:first-child {
  text-align: left;
  font-weight: normal;
  font-size: 0.875rem;
}

.trend-comparison h3 {
  font-size: 1.25rem;
  margin-bottom: 1rem;
}

/* Enhanced Performance zones for the scatter plot */
.performance-zone-needs-support {
  fill: rgba(255, 107, 107, 0.15); /* Enhanced opacity for critical zone */
  stroke: #ff6b6b;
  stroke-width: 1;
  stroke-opacity: 0.3;
}

.performance-zone-improve-retention {
  fill: rgba(255, 230, 109, 0.1); /* Naples Yellow - Improve Retention */
  stroke: #ffe66d;
  stroke-width: 0.5;
  stroke-opacity: 0.2;
}

.performance-zone-boost-fill-rate {
  fill: rgba(78, 205, 196, 0.1); /* Robin Egg Blue - Boost Fill Rate */
  stroke: #4ecdc4;
  stroke-width: 0.5;
  stroke-opacity: 0.2;
}

.performance-zone-star-zone {
  fill: rgba(26, 83, 92, 0.1); /* Midnight Green - Star Zone */
  stroke: #1a535c;
  stroke-width: 0.5;
  stroke-opacity: 0.2;
}

/* Enhanced Chart hover effects */
.chart-element {
  transition: all 0.2s ease;
}

.chart-element:hover {
  filter: brightness(1.1);
  transform: scale(1.02);
}

/* Enhanced custom tooltip styles */
.custom-tooltip {
  background: #ffffff;
  border: 1px solid #c7e0e2;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(26, 83, 92, 0.1);
  color: #1a535c;
}

/* Enhanced status badge classes */
.status-top_performer {
  background-color: #fff8dc; /* Cornsilk */
  color: #b8860b; /* DarkGoldenRod */
  border: 1px solid #ffd700; /* Gold */
}

.status-rising_star {
  background-color: #e8f8f7; /* Light Robin Egg Blue background */
  color: #1a535c; /* Midnight Green text */
  border: 1px solid #4ecdc4;
}

.status-stable {
  background-color: #f0faf9; /* Very light blue-green */
  color: #2a5a61; /* Medium green text */
  border: 1px solid #7dd3c0;
}

.status-declining {
  background-color: #fff0f0; /* Light red background */
  color: #b91c1c; /* Dark red text */
  border: 1px solid #ff6b6b;
  position: relative;
}

/* Enhanced loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #e8f4f5 25%, #f7fff7 50%, #e8f4f5 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Enhanced interactive elements */
.interactive-element {
  transition: all 0.2s ease;
  cursor: pointer;
}

.interactive-element:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(26, 83, 92, 0.1);
}

/* Enhanced focus states for accessibility */
.focus-visible:focus {
  outline: 2px solid #4ecdc4;
  outline-offset: 2px;
}

/* Enhanced bubble animations */
.bubble-main {
  transition: all 0.2s ease-in-out;
}

.bubble-group:hover .bubble-main {
  filter: brightness(1.1);
}

/* Performance zone enhancements */
.needs-support-zone {
  opacity: 0.15;
  stroke: #ff6b6b;
  stroke-width: 1;
  stroke-opacity: 0.3;
}

/* Status bar enhancements */
.status-bar-critical {
  position: relative;
  overflow: visible;
}

/* Tooltip enhancements for critical status */
.tooltip-critical {
  border: 2px solid #ff6b6b;
  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.25);
}

.recharts-wrapper {
  overflow: visible !important;
}

@keyframes pulse-border {
  0%,
  100% {
    stroke-opacity: 0.4;
    stroke-width: 2px;
  }
  50% {
    stroke-opacity: 1;
    stroke-width: 4px;
  }
}

.animate-pulse-border {
  animation: pulse-border 2.5s ease-in-out infinite;
}
