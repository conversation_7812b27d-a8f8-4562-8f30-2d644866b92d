/**
 * @file This file contains the StatusBar component, which displays a visual
 * distribution of instructor performance statuses.
 * Refactored with new color palette and Phosphor icons.
 * Enhanced with pulsing animation and count display for critical status.
 */
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Star,
  Rocket,
  Equals,
  Warning,
  ChartLine,
} from '@phosphor-icons/react';
import type { InstructorPerformance } from '@/types/instructor';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useState } from 'react';

/**
 * Props for the StatusBar component.
 * @property {InstructorPerformance[]} data - The array of prepared instructor performance data.
 * @property {function} onInstructorSelect - The function to handle instructor selection.
 */
interface StatusBarProps {
  data: InstructorPerformance[];
  onInstructorSelect: (instructor: InstructorPerformance) => void;
}

const STATUS_DISPLAY_NAMES: Record<InstructorPerformance['status'], string> = {
  top_performer: 'Exceptional',
  rising_star: 'Exceeds Expectations',
  stable: 'Meets Expectations',
  declining: 'Needs Improvement',
};

const STATUS_COLORS: Record<string, string> = {
  Exceptional: 'bg-[#FFD700]', // Gold
  'Exceeds Expectations': 'bg-[#4ecdc4]', // Robin Egg Blue
  'Meets Expectations': 'bg-[#94a3b8]', // Slate 400
  'Needs Improvement': 'bg-[#ff6b6b]', // Light Red
};

const STATUS_ICONS: Record<string, React.ReactNode> = {
  Exceptional: <Star size={16} weight="duotone" className="text-[#B8860B]" />,
  'Exceeds Expectations': (
    <Rocket size={16} weight="duotone" className="text-[#1a535c]" />
  ),
  'Meets Expectations': (
    <Equals size={16} weight="duotone" className="text-[#475569]" />
  ),
  'Needs Improvement': (
    <Warning size={16} weight="duotone" className="text-[#b91c1c]" />
  ),
};

const STATUS_ORDER = [
  'Exceptional',
  'Exceeds Expectations',
  'Meets Expectations',
  'Needs Improvement',
];

/**
 * A component that displays a segmented status bar showing the distribution
 * of instructor performance statuses using shadcn/ui components and new theme.
 * Enhanced with pulsing animation and count display for critical status visibility.
 * @param {StatusBarProps} props - The component props.
 * @returns {JSX.Element} The rendered StatusBar component.
 */
export const StatusBar = ({
  data,
  onInstructorSelect,
}: StatusBarProps): JSX.Element => {
  const [hoveredStatus, setHoveredStatus] = useState<string | null>(null);
  const totalInstructors = data.length;

  const statusGroups = data.reduce((acc, instructor) => {
    const displayName = STATUS_DISPLAY_NAMES[instructor.status] || 'Unknown';
    if (!acc[displayName]) {
      acc[displayName] = [];
    }
    acc[displayName].push(instructor);
    return acc;
  }, {} as Record<string, InstructorPerformance[]>);

  const statusCounts = Object.fromEntries(
    Object.entries(statusGroups).map(([status, instructors]) => [
      status,
      instructors.length,
    ])
  );

  const needsImprovementCount = statusCounts['Needs Improvement'] || 0;

  if (totalInstructors === 0) {
    return (
      <Card className="shadow-sm border-border">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg font-semibold">
            <ChartLine size={20} weight="duotone" className="text-primary" />
            Instructor Status Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground">No data available.</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm border-border">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg font-semibold">
          <ChartLine size={20} weight="duotone" className="text-primary" />
          Instructor Status Distribution
          {needsImprovementCount > 0 && (
            <span className="ml-2 px-2 py-1 text-xs font-bold bg-[#ff6b6b] text-white rounded-full">
              {needsImprovementCount} Need Attention
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="relative">
            <div className="flex h-4 w-full overflow-hidden rounded-full bg-muted border border-border">
              {STATUS_ORDER.map((status) => {
                const count = statusCounts[status] || 0;
                const percentage = (count / totalInstructors) * 100;
                if (percentage === 0) return null;

                const isNeedsImprovement = status === 'Needs Improvement';

                return (
                  <div
                    key={status}
                    style={{ width: `${percentage}%` }}
                    className={`${STATUS_COLORS[status]} transition-all duration-300 hover:brightness-110 relative flex items-center justify-center`}
                    title={`${status}: ${count} (${(percentage ?? 0).toFixed(
                      1
                    )}%)`}
                  >
                    {/* Count display directly on segment for critical status */}
                    {isNeedsImprovement && count > 0 && percentage > 8 && (
                      <span className="text-xs font-bold text-white drop-shadow-sm">
                        {count}
                      </span>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            {STATUS_ORDER.map((status) => {
              const count = statusCounts[status] || 0;
              const percentage = (count / totalInstructors) * 100;
              const isNeedsImprovement = status === 'Needs Improvement';
              const instructorsInStatus = statusGroups[status] || [];
              const isHovered = hoveredStatus === status;

              return (
                <TooltipProvider key={status} delayDuration={100}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div
                        className={`flex items-center justify-between p-3 rounded-lg border border-border/50 interactive-element transition-all duration-200 cursor-pointer hover:shadow-md ${
                          isNeedsImprovement && count > 0
                            ? 'ring-2 ring-[#ff6b6b] ring-opacity-50'
                            : ''
                        } ${
                          isHovered
                            ? 'bg-slate-200 hover:bg-slate-300'
                            : 'bg-slate-100'
                        }`}
                        onMouseEnter={() => setHoveredStatus(status)}
                        onMouseLeave={() => setHoveredStatus(null)}
                      >
                        <div className="flex items-center gap-2">
                          {STATUS_ICONS[status]}
                          <span className="text-sm font-medium text-foreground">
                            {status}
                          </span>
                          {isNeedsImprovement && count > 0 && (
                            <span className="ml-1 text-xs font-bold text-[#ff6b6b]">
                              ⚠️ Action Required
                            </span>
                          )}
                        </div>
                        <div className="text-right">
                          <div
                            className={`text-sm font-bold ${
                              isNeedsImprovement && count > 0
                                ? 'text-[#ff6b6b]'
                                : 'text-foreground'
                            }`}
                          >
                            {count}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {(percentage ?? 0).toFixed(1)}%
                          </div>
                        </div>
                      </div>
                    </TooltipTrigger>
                    {instructorsInStatus.length > 0 && (
                      <TooltipContent className="z-50 w-60 rounded-lg border border-border bg-white p-2 text-foreground shadow-lg">
                        <div className="flex flex-col gap-1">
                          <p className="mb-1 border-b border-border/50 pb-1 text-center font-bold">
                            {status}
                          </p>
                          <ul className="max-h-60 overflow-y-auto">
                            {instructorsInStatus.map((instructor) => (
                              <li key={instructor.instructorId}>
                                <button
                                  onClick={() => onInstructorSelect(instructor)}
                                  className="w-full rounded-md p-2 text-left transition-all duration-200 hover:bg-slate-200 hover:shadow-sm active:bg-slate-200"
                                >
                                  {instructor.name}
                                </button>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>
              );
            })}
          </div>

          <div className="pt-2 border-t border-border/50">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Total Instructors:</span>
              <span className="font-semibold text-foreground">
                {totalInstructors}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
