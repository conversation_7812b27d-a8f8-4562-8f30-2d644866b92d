import { Responsive<PERSON>ontainer, AreaChart, Area } from 'recharts';
import {
  ChartLine,
  Users,
  Star,
  TrendUp,
  ArrowRight,
  Warning,
  Info,
} from '@phosphor-icons/react';
import { type InstructorPerformance } from '../../types/instructor';

// biome-ignore lint/suspicious/noExplicitAny: This is a Recharts component prop
type CustomTooltipProps = any;

/**
 * A custom tooltip component for the Recharts scatter plot.
 * It displays an instructor's name, performance score, a 30-day trend sparkline,
 * and key performance metrics.
 * Refactored with new color palette and enhanced design.
 * Enhanced with information hierarchy and critical metrics color-coding.
 */
export const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
  if (!active || !payload || !payload[0]) {
    return null;
  }

  const instructor: InstructorPerformance = payload[0].payload;

  // Define thresholds for critical performance identification
  const THRESHOLDS = {
    fillRate: 50, // Below 50% is concerning
    retention: 60, // Below 60% is concerning
    satisfaction: 3.5, // Below 3.5/5 is concerning
    performanceScore: 60, // Below 60/100 is concerning
  };

  // Helper function to determine if a metric is critical
  const isCritical = {
    fillRate: (instructor.fillRate ?? 0) < THRESHOLDS.fillRate,
    retention: (instructor.retention ?? 0) < THRESHOLDS.retention,
    satisfaction: (instructor.satisfaction ?? 0) < THRESHOLDS.satisfaction,
    performance:
      (instructor.performanceScore ?? 0) < THRESHOLDS.performanceScore,
  };

  const isInstructorAtRisk =
    instructor.status === 'declining' ||
    Object.values(isCritical).some(Boolean);

  return (
    <div
      className="w-72 rounded-lg border border-border bg-card p-4 shadow-lg"
      style={{
        fontFamily: 'Montserrat, sans-serif',
        backdropFilter: 'blur(12px)',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        boxShadow: '0 8px 25px rgba(26, 83, 92, 0.1)',
      }}
    >
      {/* Header with enhanced styling for critical status */}
      <div
        className={`mb-3 flex items-center justify-between border-b pb-3 ${
          isInstructorAtRisk ? 'border-[#ff6b6b]/20' : 'border-border'
        }`}
      >
        <div className="flex items-center gap-2">
          <h3 className="font-semibold text-foreground">{instructor.name}</h3>
          {isInstructorAtRisk && (
            <Warning size={16} weight="duotone" className="text-[#ff6b6b]" />
          )}
        </div>
        <div className="flex items-baseline gap-1">
          <span
            className={`text-2xl font-bold ${
              isCritical.performance ? 'text-[#ff6b6b]' : 'text-primary'
            }`}
          >
            {Math.round(instructor.performanceScore ?? 0)}
          </span>
          <span className="text-sm text-muted-foreground">/100</span>
        </div>
      </div>

      {/* Performance Score Alert for Critical Status */}
      {isCritical.performance && (
        <div className="mb-3 flex items-center gap-2 rounded-md bg-red-100 p-2 border border-red-200">
          <Warning size={14} weight="duotone" className="text-[#b91c1c]" />
          <span className="text-xs font-medium text-[#b91c1c]">
            Performance score below threshold ({THRESHOLDS.performanceScore}%)
          </span>
        </div>
      )}

      {/* 30-day trend with enhanced styling for critical status */}
      <div className="my-3">
        <ResponsiveContainer width="100%" height={40}>
          <AreaChart data={instructor.last30Days}>
            <defs>
              <linearGradient
                id={`sparkline-gradient-${instructor.instructorId}`}
                x1="0"
                y1="0"
                x2="0"
                y2="1"
              >
                <stop
                  offset="5%"
                  stopColor={isCritical.performance ? '#ff6b6b' : '#4ecdc4'}
                  stopOpacity={0.4}
                />
                <stop
                  offset="95%"
                  stopColor={isCritical.performance ? '#ff6b6b' : '#4ecdc4'}
                  stopOpacity={0}
                />
              </linearGradient>
            </defs>
            <Area
              type="monotone"
              dataKey="score"
              stroke={isCritical.performance ? '#ff6b6b' : '#1a535c'}
              fill={`url(#sparkline-gradient-${instructor.instructorId})`}
              strokeWidth={isCritical.performance ? 3 : 2}
            />
          </AreaChart>
        </ResponsiveContainer>
        <span className="mt-1 block text-center text-xs text-muted-foreground">
          30-day trend
        </span>
      </div>

      {/* Enhanced metrics with critical status indicators */}
      <div className="mt-3 flex flex-col gap-3">
        {/* Fill Rate */}
        <div
          className={`flex items-center justify-between text-sm p-2 rounded ${
            isCritical.fillRate ? 'bg-red-100' : ''
          }`}
        >
          <span className="flex items-center gap-2">
            {isCritical.fillRate ? (
              <Warning size={16} weight="duotone" className="text-[#ff6b6b]" />
            ) : (
              <ChartLine
                size={16}
                weight="duotone"
                className="text-[#4ecdc4]"
              />
            )}
            <span
              className={`font-medium ${
                isCritical.fillRate ? 'text-[#b91c1c]' : 'text-muted-foreground'
              }`}
            >
              Fill Rate
              {isCritical.fillRate && (
                <span className="ml-1 text-xs font-bold text-[#ff6b6b]">
                  ⚠️ LOW
                </span>
              )}
            </span>
          </span>
          <span
            className={`font-semibold ${
              isCritical.fillRate ? 'text-[#ff6b6b]' : 'text-foreground'
            }`}
          >
            {(instructor.fillRate ?? 0).toFixed(1)}%
          </span>
        </div>

        {/* Client Retention */}
        <div
          className={`flex items-center justify-between text-sm p-2 rounded ${
            isCritical.retention ? 'bg-red-100' : ''
          }`}
        >
          <span className="flex items-center gap-2">
            {isCritical.retention ? (
              <Warning size={16} weight="duotone" className="text-[#ff6b6b]" />
            ) : (
              <Users size={16} weight="duotone" className="text-[#7dd3c0]" />
            )}
            <span
              className={`font-medium ${
                isCritical.retention
                  ? 'text-[#b91c1c]'
                  : 'text-muted-foreground'
              }`}
            >
              Retention
              {isCritical.retention && (
                <span className="ml-1 text-xs font-bold text-[#ff6b6b]">
                  ⚠️ LOW
                </span>
              )}
            </span>
          </span>
          <span
            className={`font-semibold ${
              isCritical.retention ? 'text-[#ff6b6b]' : 'text-foreground'
            }`}
          >
            {(instructor.retention ?? 0).toFixed(1)}%
          </span>
        </div>

        {/* Client Satisfaction */}
        <div
          className={`flex items-center justify-between text-sm p-2 rounded ${
            isCritical.satisfaction ? 'bg-red-100' : ''
          }`}
        >
          <span className="flex items-center gap-2">
            {isCritical.satisfaction ? (
              <Warning size={16} weight="duotone" className="text-[#ff6b6b]" />
            ) : (
              <Star size={16} weight="duotone" className="text-[#ffe66d]" />
            )}
            <span
              className={`font-medium ${
                isCritical.satisfaction
                  ? 'text-[#b91c1c]'
                  : 'text-muted-foreground'
              }`}
            >
              Rating
              {isCritical.satisfaction && (
                <span className="ml-1 text-xs font-bold text-[#ff6b6b]">
                  ⚠️ LOW
                </span>
              )}
            </span>
          </span>
          <span
            className={`font-semibold ${
              isCritical.satisfaction ? 'text-[#ff6b6b]' : 'text-foreground'
            }`}
          >
            {(instructor.satisfaction ?? 0).toFixed(1)}/5
          </span>
        </div>

        {/* Month-over-Month Change */}
        <div className="flex items-center justify-between text-sm p-2 rounded">
          <span className="flex items-center gap-2 text-muted-foreground">
            <TrendUp size={16} weight="duotone" className="text-primary" />
            <span className="font-medium">MoM Change</span>
          </span>
          <span
            className={`font-semibold ${
              (instructor.monthOverMonthChange ?? 0) >= 0
                ? 'text-[#4ecdc4]'
                : 'text-[#ff6b6b]'
            }`}
          >
            {(instructor.monthOverMonthChange ?? 0) > 0 ? '+' : ''}
            {(instructor.monthOverMonthChange ?? 0).toFixed(1)}%
          </span>
        </div>
      </div>

      {/* Enhanced footer with action-oriented messaging */}
      <div
        className={`mt-3 border-t pt-3 ${
          isInstructorAtRisk ? 'border-[#ff6b6b]/20' : 'border-border'
        }`}
      >
        <div className="flex items-center justify-center gap-2 text-xs font-medium">
          {isInstructorAtRisk ? (
            <>
              <Warning size={12} weight="duotone" className="text-[#ff6b6b]" />
              <span className="text-[#b91c1c]">Click for action plan</span>
            </>
          ) : (
            <>
              <Info size={12} weight="duotone" className="text-accent" />
              <span className="text-accent">Click for detailed analysis</span>
            </>
          )}
          <ArrowRight size={12} weight="duotone" />
        </div>
      </div>
    </div>
  );
};
