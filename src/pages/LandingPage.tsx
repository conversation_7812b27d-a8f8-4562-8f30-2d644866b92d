import { SignUpButton, SignInButton } from '@clerk/clerk-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ChartScatter, TrendUp, Star } from '@phosphor-icons/react';

/**
 * Landing Page component for unauthenticated users
 * Refactored with new design system, Montserrat font, and new color palette
 */
export default function LandingPage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-background font-sans">
      <div className="text-center p-8 max-w-4xl mx-auto">
        <div className="flex items-center justify-center gap-4 mb-6">
          <ChartScatter size={64} weight="duotone" className="text-primary" />
          <div className="h-16 w-1 bg-accent rounded-full"></div>
          <div className="flex flex-col items-center gap-2">
            <TrendUp size={32} weight="duotone" className="text-accent" />
            <Star size={24} weight="duotone" className="text-[#ffe66d]" />
          </div>
        </div>

        <h1 className="text-5xl font-bold text-foreground mb-4">
          Instructor Performance Hub
        </h1>
        <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
          Unlock data-driven insights into instructor performance with our
          comprehensive analytics dashboard featuring the innovative Performance
          Galaxy visualization.
        </p>

        <Card className="mb-8 shadow-sm border-border bg-card/50">
          <CardContent className="p-6">
            <div className="grid grid-cols-3 gap-6 text-center">
              <div className="space-y-2">
                <ChartScatter
                  size={32}
                  weight="duotone"
                  className="text-[#4ecdc4] mx-auto"
                />
                <h3 className="font-semibold text-foreground">
                  Visual Analytics
                </h3>
                <p className="text-sm text-muted-foreground">
                  Interactive scatter plots and performance zones
                </p>
              </div>
              <div className="space-y-2">
                <TrendUp
                  size={32}
                  weight="duotone"
                  className="text-[#7dd3c0] mx-auto"
                />
                <h3 className="font-semibold text-foreground">
                  Real-time Insights
                </h3>
                <p className="text-sm text-muted-foreground">
                  Live performance tracking and trends
                </p>
              </div>
              <div className="space-y-2">
                <Star
                  size={32}
                  weight="duotone"
                  className="text-[#ffe66d] mx-auto"
                />
                <h3 className="font-semibold text-foreground">
                  Performance Scoring
                </h3>
                <p className="text-sm text-muted-foreground">
                  Industry-standard weighted metrics
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-center gap-4">
          <Button size="lg" className="btn-primary interactive-element" asChild>
            <SignInButton mode="modal">Sign In to Dashboard</SignInButton>
          </Button>
          <Button
            size="lg"
            variant="outline"
            className="btn-outline interactive-element"
            asChild
          >
            <SignUpButton mode="modal">Create Account</SignUpButton>
          </Button>
        </div>

        <p className="text-sm text-muted-foreground mt-6">
          Powered by modern web technologies with real-time data synchronization
        </p>
      </div>
    </div>
  );
}
