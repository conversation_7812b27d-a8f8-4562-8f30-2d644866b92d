import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Safely formats a number to a fixed number of decimal places.
 * Returns "0" if the input is null, undefined, or not a valid number.
 * @param value - The number to format
 * @param digits - Number of decimal places (default: 1)
 * @returns Formatted string or "0" if invalid
 */
export function safeToFixed(
  value: number | null | undefined,
  digits: number = 1
): string {
  if (typeof value !== 'number' || isNaN(value)) {
    return '0'.padEnd(digits + 2, '.0');
  }
  return value.toFixed(digits);
}

/**
 * Safely formats a percentage with appropriate handling of undefined/null values.
 * @param value - The percentage value (0-100)
 * @param digits - Number of decimal places (default: 1)
 * @returns Formatted percentage string
 */
export function safePercentage(
  value: number | null | undefined,
  digits: number = 1
): string {
  return `${safeToFixed(value, digits)}%`;
}

/**
 * Safely formats a rating with appropriate handling of undefined/null values.
 * @param value - The rating value (typically 1-5)
 * @param maxRating - Maximum rating value (default: 5)
 * @param digits - Number of decimal places (default: 1)
 * @returns Formatted rating string
 */
export function safeRating(
  value: number | null | undefined,
  maxRating: number = 5,
  digits: number = 1
): string {
  return `${safeToFixed(value, digits)}/${maxRating}`;
}
