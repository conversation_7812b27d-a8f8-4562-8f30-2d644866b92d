import { Authenticated, Unauthenticated } from 'convex/react';
import LandingPage from './pages/LandingPage';
import Layout from './components/Layout';
import PerformanceDashboard from './components/Dashboard/PerformanceDashboard';
import { useAuth } from '@clerk/clerk-react';
import { Toaster, toast } from 'sonner';
import { useEffect } from 'react';

const clerkKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

function App() {
  const { isLoaded, isSignedIn } = useAuth();

  useEffect(() => {
    toast.info('Debug Info', {
      duration: 2000,
      description: (
        <div style={{ color: 'black', fontSize: '14px' }}>
          Clerk Key:{' '}
          {clerkKey ? (
            <span style={{ color: 'green' }}>SET</span>
          ) : (
            <span style={{ color: 'red', fontWeight: 'bold' }}>
              !!! UNDEFINED !!!
            </span>
          )}
          <br />
          Clerk Loaded:{' '}
          <span style={{ fontWeight: 'bold' }}>{String(isLoaded)}</span>
          <br />
          Clerk Signed In:{' '}
          <span style={{ fontWeight: 'bold' }}>{String(isSignedIn)}</span>
        </div>
      ),
      position: 'top-center',
    });
  }, [isLoaded, isSignedIn]);

  return (
    <main className="h-screen">
      <Toaster />
      <Unauthenticated>
        <LandingPage />
      </Unauthenticated>
      <Authenticated>
        <Layout>
          <PerformanceDashboard />
        </Layout>
      </Authenticated>
    </main>
  );
}

export default App;
