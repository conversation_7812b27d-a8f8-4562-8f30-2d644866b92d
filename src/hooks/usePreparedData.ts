/**
 * @file This file contains the `usePreparedData` hook, which fetches raw
 * data from the Convex backend and processes it into the format required by
 * the performance dashboard components.
 */
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import type { InstructorPerformance } from '@/types/instructor';

/**
 * A custom hook to fetch and prepare instructor performance data.
 * All calculations are now performed on the backend; this hook simply
 * fetches the data and provides a stable, empty array during initial load.
 * @returns {InstructorPerformance[]} An array of prepared instructor data, ready for visualization.
 */
export const usePreparedData = (): InstructorPerformance[] => {
  const preparedData = useQuery(api.instructors.getPerformanceDashboardData);

  // Return the data from the backend, or an empty array if it's still loading.
  return preparedData ?? [];
};
