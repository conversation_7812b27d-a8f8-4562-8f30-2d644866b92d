/** @type {import('tailwindcss').Config} */
const plugin = require('tailwindcss/plugin');

module.exports = {
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Montserrat', 'ui-sans-serif', 'system-ui', 'sans-serif'],
      },
    },
  },
  plugins: [
    plugin(function ({ addBase }) {
      addBase({
        ':root': {
          '--background': '#f7fff7',
          '--foreground': '#1a535c',
          '--card': '#ffffff',
          '--card-foreground': '#1a535c',
          '--primary': '#1a535c',
          '--primary-foreground': '#f7fff7',
          '--secondary': '#e8f8f7',
          '--secondary-foreground': '#1a535c',
          '--accent': '#4ecdc4',
          '--accent-foreground': '#1a535c',
          '--muted': '#e8f4f5',
          '--muted-foreground': '#4a6b70',
          '--border': '#c7e0e2',
          '--input': '#e8f4f5',
          '--ring': '#4ecdc4',
          '--destructive': '#ff6b6b',
          '--destructive-foreground': '#ffffff',
          '--popover': '#ffffff',
          '--popover-foreground': '#1a535c',
          '--radius': '0.5rem',
        },
        '.dark': {
          '--background': '222.2 84% 4.9%',
          '--foreground': '210 40% 98%',
          '--card': '222.2 84% 4.9%',
          '--card-foreground': '210 40% 98%',
          '--popover': '222.2 84% 4.9%',
          '--popover-foreground': '210 40% 98%',
          '--primary': '210 40% 98%',
          '--primary-foreground': '222.2 47.4% 11.2%',
          '--secondary': '217.2 32.6% 17.5%',
          '--secondary-foreground': '210 40% 98%',
          '--muted': '217.2 32.6% 17.5%',
          '--muted-foreground': '215 20.2% 65.1%',
          '--accent': '217.2 32.6% 17.5%',
          '--accent-foreground': '210 40% 98%',
          '--destructive': '0 62.8% 30.6%',
          '--destructive-foreground': '210 40% 98%',
          '--border': '217.2 32.6% 17.5%',
          '--input': '217.2 32.6% 17.5%',
          '--ring': '212.7 26.8% 83.9%',
        },
      });
    }),
  ],
};
